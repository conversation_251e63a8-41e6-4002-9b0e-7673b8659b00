# 🤖 CODY - Advanced Context-Aware AI Coding Assistant

CODY is an advanced, context-aware, AI-powered CLI agent for code assistance that supports natural language processing, multi-language coding, autonomous debugging, and advanced file handling.

## ✨ Features

### 🧠 Core Capabilities
- **Natural Language Processing**: Understands commands in English, Hindi, and mixed languages
- **Multi-threaded Execution**: Concurrent task handling for better performance
- **Context-Aware Operations**: Maintains conversation context and project understanding
- **Autonomous Debugging**: Automatic error detection and fixing suggestions
- **Predictive Prefetching**: Smart caching and suggestion system

### 🔧 Advanced Code Operations
- **AST-based Code Analysis**: Deep code structure understanding
- **Multi-language Support**: Python, JavaScript, TypeScript, Java, C++, Go, Rust
- **Smart Code Search**: Regex and semantic search capabilities
- **Code Refactoring**: Context-aware code improvements
- **Test Generation**: Automated unit test creation

### 🚀 NEW: Advanced AI Coding Features
- **Advanced Pattern Search**: Semantic understanding with fuzzy matching and natural language queries
- **Smart Code Replacement**: Intelligent refactoring with preview, Git integration, and complex operations
- **Smart File Search (grep++)**: Context-aware content search with syntax understanding and semantic ranking
- **Auto Planner**: Autonomous task decomposition and execution with progress tracking and Git integration
- **Input Fixer**: Automatic code fixing for syntax errors, formatting issues, and malformed input across multiple languages
- **File Indexing**: Efficient codebase indexing with semantic understanding, real-time updates, and intelligent navigation

### 🌐 Web Integration
- **Real-time Web Search**: Stack Overflow, GitHub, official documentation
- **RAG Integration**: Retrieval Augmented Generation for better responses
- **Documentation Lookup**: Instant access to programming resources

### 🛠️ Development Tools
- **Git Integration**: Full workflow support with intelligent staging
- **Terminal Integration**: Cross-platform command execution
- **File Operations**: Advanced file handling with fuzzy matching
- **Static Analysis**: Code quality assessment and suggestions

## 🚀 Quick Start

### Installation

1. **Clone the repository:**
```bash
git clone <repository-url>
cd CODY
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Set up environment variables:**
```bash
cp .env.example .env
# Edit .env with your API keys
```

4. **Run CODY:**
```bash
python agent.py
```

### Environment Variables

Create a `.env` file with the following:

```env
# DeepSeek API (required)
DEEPSEEK_API_KEY=your_deepseek_api_key

# Gemini API (optional, for enhanced NLP)
GEMINI_API_KEY=your_gemini_api_key

# Web search (optional)
SERPAPI_KEY=your_serpapi_key
```

## 📖 Usage Examples

### Natural Language Commands

```bash
# Create files
"Mujhe ek Python login system chahiye"
"Create a JavaScript function for API calls"

# Debug code
"Fix the error in main.py"
"Debug this TypeError in my code"

# Search and analyze
"Search for authentication examples"
"Analyze the complexity of this function"

# Refactor code
"Improve the performance of this code"
"Extract this into smaller functions"
```

### Command Reference

| Command | Description |
|---------|-------------|
| `/add <file>` | Add file to context |
| `/search <query>` | Web search for programming help |
| `/debug <file>` | Analyze and debug file |
| `/analyze <file>` | Perform code analysis |
| `/refactor <file>` | Suggest code improvements |
| `/test <file>` | Generate unit tests |
| `/help` | Show all commands |

### 🆕 Advanced AI Coding Commands

| Command | Description |
|---------|-------------|
| `advanced_pattern_search()` | Search codebase with semantic understanding and fuzzy matching |
| `smart_replace_code()` | Intelligent code replacement with preview and Git integration |
| `smart_file_search()` | Advanced grep-like search with context awareness |
| `create_execution_plan()` | Create autonomous task execution plans |
| `execute_plan()` | Execute previously created plans with progress tracking |
| `fix_code_input()` | Automatically fix malformed code and syntax errors |
| `build_file_index()` | Build efficient codebase index for fast navigation |
| `search_file_index()` | Search indexed codebase with semantic understanding |

**Example Usage:**
```python
# Search for complex patterns across the codebase
advanced_pattern_search(
    query="gemini.*model|authentication.*jwt",
    search_mode="hybrid",
    file_types=[".py", ".js"],
    max_results=20
)

# Intelligently replace code with preview
smart_replace_code(
    search_pattern="old_function_name",
    replacement="new_function_name",
    replacement_mode="function_signature",
    preview_only=True,
    git_commit=False
)

# Create and execute autonomous plans
plan = create_execution_plan(
    user_request="Add user authentication with JWT tokens",
    strategy="waterfall",
    auto_execute=True
)
```

## 🏗️ Architecture

### Core Modules

```
core/
├── nlp_processor.py           # Natural language understanding
├── code_analyzer.py           # AST parsing and analysis
├── autonomous_debugger.py     # Error detection and fixing
├── web_search_rag.py          # Web search and RAG
├── task_manager.py            # Multi-threading and caching
├── advanced_pattern_search.py # Semantic pattern search with fuzzy matching
├── smart_code_replacer.py     # Intelligent code replacement with Git integration
├── smart_file_search.py       # Context-aware file search (grep++)
├── auto_planner.py            # Autonomous task decomposition and execution
├── input_fixer.py             # Automatic code fixing and formatting
└── file_indexer.py            # Efficient codebase indexing with semantic understanding
```

### Workflow

1. **Execute**: Perform the requested action
2. **Analyze**: Examine results and context
3. **Plan**: Determine next steps based on analysis
4. **Execute**: Continue with iterative improvements

## 🔧 Advanced Configuration

### Performance Tuning

```python
# In agent.py, adjust these constants:
MAX_CONCURRENT_TASKS = 4        # Concurrent task limit
CACHE_SIZE_MB = 100            # Cache size
ENABLE_PREDICTIVE_PREFETCHING = True  # Smart prefetching
```

### Language Support

CODY supports multiple programming languages with specialized handling:

- **Python**: Full AST analysis, pylint integration
- **JavaScript/TypeScript**: Syntax analysis, npm integration
- **Java**: Class structure analysis
- **C/C++**: Header analysis, compilation support
- **Go**: Module analysis, go tools integration
- **Rust**: Cargo integration, ownership analysis

## 📚 Advanced Features Documentation

For detailed documentation on the new advanced AI coding features, see:

- **[Advanced Features Guide](docs/ADVANCED_FEATURES.md)** - Comprehensive documentation for all advanced features
- **[API Reference](docs/API_REFERENCE.md)** - Detailed API documentation for each module
- **[Integration Examples](examples/)** - Practical examples and use cases

## 🧪 Testing

Run the test suite:

```bash
# Unit tests
pytest tests/

# Integration tests
pytest tests/integration/

# Performance tests
pytest tests/performance/

# Test advanced features integration
python test_advanced_features.py
```

## 📊 Performance Metrics

CODY tracks and optimizes:

- Task execution times
- Cache hit rates
- Context usage efficiency
- Memory consumption
- Response accuracy

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

### Development Setup

```bash
# Install development dependencies
pip install -r requirements-dev.txt

# Run code formatting
black .
isort .

# Run linting
pylint core/ agent.py
```

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- DeepSeek AI for the powerful language model
- OpenAI for the API standards
- The open-source community for various tools and libraries

## 📞 Support

For support and questions:

- Create an issue on GitHub
- Check the documentation
- Join our community discussions

---

**CODY** - Making coding smarter, faster, and more intuitive! 🚀
