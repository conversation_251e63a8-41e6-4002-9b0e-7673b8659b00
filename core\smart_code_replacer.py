#!/usr/bin/env python3

"""
Smart Code Replacement Module for CODY Agent
Provides intelligent string and code replacement with preview, Git integration,
and support for complex refactoring operations.
"""

import os
import re
import shutil
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import logging
import tempfile
import json

logger = logging.getLogger('CODY.SmartCodeReplacer')

class ReplacementMode(Enum):
    """Types of replacement operations."""
    SIMPLE = "simple"
    REGEX = "regex"
    FUNCTION_SIGNATURE = "function_signature"
    VARIABLE_RENAME = "variable_rename"
    CLASS_RENAME = "class_rename"
    IMPORT_UPDATE = "import_update"

@dataclass
class ReplacementResult:
    """Result of a replacement operation."""
    file_path: str
    original_content: str
    new_content: str
    changes_made: int
    success: bool
    error_message: Optional[str] = None
    backup_path: Optional[str] = None

@dataclass
class ReplacementPreview:
    """Preview of replacement changes."""
    file_path: str
    line_number: int
    original_line: str
    new_line: str
    context_before: List[str] = field(default_factory=list)
    context_after: List[str] = field(default_factory=list)

class SmartCodeReplacer:
    """
    Smart code replacement engine with preview, backup, and Git integration.
    """
    
    def __init__(self, root_path: str = "."):
        self.root_path = Path(root_path)
        self.backup_dir = self.root_path / ".cody_backups"
        self.backup_dir.mkdir(exist_ok=True)
        
        # Language-specific patterns for intelligent replacement
        self.language_patterns = {
            'python': {
                'function_def': r'def\s+(\w+)\s*\(',
                'class_def': r'class\s+(\w+)\s*[\(:]',
                'variable_assignment': r'(\w+)\s*=\s*',
                'import_statement': r'(?:from\s+\w+\s+)?import\s+([^\n]+)',
                'function_call': r'(\w+)\s*\(',
            },
            'javascript': {
                'function_def': r'(?:function\s+(\w+)|(\w+)\s*=\s*(?:function|\([^)]*\)\s*=>))',
                'class_def': r'class\s+(\w+)',
                'variable_assignment': r'(?:let|const|var)\s+(\w+)',
                'import_statement': r'import\s+.*?from\s+[\'"]([^\'"]+)[\'"]',
                'function_call': r'(\w+)\s*\(',
            },
            'java': {
                'function_def': r'(?:public|private|protected)?\s*(?:static)?\s*\w+\s+(\w+)\s*\(',
                'class_def': r'(?:public|private)?\s*class\s+(\w+)',
                'import_statement': r'import\s+([^;]+);',
                'variable_assignment': r'\w+\s+(\w+)\s*=',
            }
        }

    def replace_code(self, search_pattern: str, replacement: str, 
                    file_paths: Optional[List[str]] = None, preview_only: bool = True,
                    backup_files: bool = True, git_commit: bool = False,
                    replacement_mode: ReplacementMode = ReplacementMode.SIMPLE) -> Tuple[List[ReplacementResult], List[ReplacementPreview]]:
        """
        Perform intelligent code replacement with preview and backup options.
        
        Args:
            search_pattern: Pattern to search for
            replacement: Replacement text or code
            file_paths: Specific files to modify (optional)
            preview_only: Only show preview without making changes
            backup_files: Create backup files before modification
            git_commit: Automatically commit changes to Git
            replacement_mode: Type of replacement operation
            
        Returns:
            Tuple of (replacement results, preview changes)
        """
        results = []
        previews = []
        
        # Get files to process
        files_to_process = self._get_files_to_process(file_paths)
        
        for file_path in files_to_process:
            try:
                # Read file content
                with open(file_path, 'r', encoding='utf-8') as f:
                    original_content = f.read()
                
                # Perform replacement based on mode
                new_content, file_previews = self._perform_replacement(
                    original_content, search_pattern, replacement, 
                    replacement_mode, str(file_path)
                )
                
                previews.extend(file_previews)
                
                if new_content != original_content:
                    if not preview_only:
                        # Create backup if requested
                        backup_path = None
                        if backup_files:
                            backup_path = self._create_backup(file_path, original_content)
                        
                        # Write new content
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(new_content)
                        
                        # Count changes
                        changes_made = len([p for p in file_previews if p.original_line != p.new_line])
                        
                        results.append(ReplacementResult(
                            file_path=str(file_path),
                            original_content=original_content,
                            new_content=new_content,
                            changes_made=changes_made,
                            success=True,
                            backup_path=backup_path
                        ))
                        
                        logger.info(f"Successfully replaced {changes_made} occurrences in {file_path}")
                    else:
                        # Preview mode - just count potential changes
                        changes_made = len([p for p in file_previews if p.original_line != p.new_line])
                        results.append(ReplacementResult(
                            file_path=str(file_path),
                            original_content=original_content,
                            new_content=new_content,
                            changes_made=changes_made,
                            success=True
                        ))
                        
            except Exception as e:
                logger.error(f"Error processing file {file_path}: {e}")
                results.append(ReplacementResult(
                    file_path=str(file_path),
                    original_content="",
                    new_content="",
                    changes_made=0,
                    success=False,
                    error_message=str(e)
                ))
        
        # Git commit if requested and not preview mode
        if git_commit and not preview_only and any(r.success for r in results):
            self._git_commit_changes(search_pattern, replacement, results)
        
        return results, previews

    def _get_files_to_process(self, file_paths: Optional[List[str]]) -> List[Path]:
        """Get list of files to process."""
        if file_paths:
            return [Path(fp) for fp in file_paths if Path(fp).exists()]
        
        # Default: search all code files in the project
        code_extensions = {'.py', '.js', '.jsx', '.ts', '.tsx', '.java', '.cpp', '.c', '.h', '.hpp', '.go', '.rs', '.php', '.rb', '.cs', '.swift', '.kt'}
        files = []
        
        for file_path in self.root_path.rglob('*'):
            if (file_path.is_file() and 
                file_path.suffix in code_extensions and
                not any(excluded in file_path.parts for excluded in ['__pycache__', '.git', 'node_modules', '.venv'])):
                files.append(file_path)
        
        return files

    def _perform_replacement(self, content: str, search_pattern: str, replacement: str,
                           mode: ReplacementMode, file_path: str) -> Tuple[str, List[ReplacementPreview]]:
        """Perform the actual replacement based on mode."""
        previews = []
        lines = content.splitlines()
        new_lines = lines.copy()
        
        if mode == ReplacementMode.SIMPLE:
            # Simple string replacement
            for i, line in enumerate(lines):
                if search_pattern in line:
                    new_line = line.replace(search_pattern, replacement)
                    if new_line != line:
                        previews.append(self._create_preview(file_path, i + 1, line, new_line, lines, i))
                        new_lines[i] = new_line
        
        elif mode == ReplacementMode.REGEX:
            # Regex replacement
            try:
                pattern = re.compile(search_pattern, re.MULTILINE)
                for i, line in enumerate(lines):
                    new_line = pattern.sub(replacement, line)
                    if new_line != line:
                        previews.append(self._create_preview(file_path, i + 1, line, new_line, lines, i))
                        new_lines[i] = new_line
            except re.error as e:
                logger.error(f"Invalid regex pattern: {search_pattern}, error: {e}")
        
        elif mode == ReplacementMode.FUNCTION_SIGNATURE:
            # Function signature replacement
            previews.extend(self._replace_function_signature(lines, new_lines, search_pattern, replacement, file_path))
        
        elif mode == ReplacementMode.VARIABLE_RENAME:
            # Variable renaming with scope awareness
            previews.extend(self._replace_variable_name(lines, new_lines, search_pattern, replacement, file_path))
        
        return '\n'.join(new_lines), previews

    def _create_preview(self, file_path: str, line_number: int, original_line: str,
                       new_line: str, all_lines: List[str], line_index: int) -> ReplacementPreview:
        """Create a preview of the replacement."""
        context_before = []
        context_after = []

        # Get context lines
        for i in range(max(0, line_index - 2), line_index):
            context_before.append(all_lines[i])

        for i in range(line_index + 1, min(len(all_lines), line_index + 3)):
            context_after.append(all_lines[i])

        return ReplacementPreview(
            file_path=file_path,
            line_number=line_number,
            original_line=original_line,
            new_line=new_line,
            context_before=context_before,
            context_after=context_after
        )

    def _replace_function_signature(self, lines: List[str], new_lines: List[str],
                                  old_name: str, new_name: str, file_path: str) -> List[ReplacementPreview]:
        """Replace function signatures intelligently."""
        previews = []

        # Detect language
        language = self._detect_language(file_path)
        if language not in self.language_patterns:
            return previews

        function_pattern = self.language_patterns[language].get('function_def')
        if not function_pattern:
            return previews

        for i, line in enumerate(lines):
            match = re.search(function_pattern, line)
            if match and old_name in match.groups():
                new_line = line.replace(old_name, new_name)
                if new_line != line:
                    previews.append(self._create_preview(file_path, i + 1, line, new_line, lines, i))
                    new_lines[i] = new_line

        return previews

    def _replace_variable_name(self, lines: List[str], new_lines: List[str],
                             old_name: str, new_name: str, file_path: str) -> List[ReplacementPreview]:
        """Replace variable names with scope awareness."""
        previews = []

        # Simple variable replacement (can be enhanced with AST parsing)
        for i, line in enumerate(lines):
            # Use word boundaries to avoid partial matches
            pattern = r'\b' + re.escape(old_name) + r'\b'
            if re.search(pattern, line):
                new_line = re.sub(pattern, new_name, line)
                if new_line != line:
                    previews.append(self._create_preview(file_path, i + 1, line, new_line, lines, i))
                    new_lines[i] = new_line

        return previews

    def _detect_language(self, file_path: str) -> str:
        """Detect programming language from file extension."""
        extension = Path(file_path).suffix.lower()
        language_map = {
            '.py': 'python',
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.ts': 'javascript',
            '.tsx': 'javascript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'cpp',
            '.h': 'cpp',
            '.hpp': 'cpp'
        }
        return language_map.get(extension, 'unknown')

    def _create_backup(self, file_path: Path, content: str) -> str:
        """Create a backup of the file."""
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{file_path.name}_{timestamp}.backup"
        backup_path = self.backup_dir / backup_name

        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)

        return str(backup_path)

    def _git_commit_changes(self, search_pattern: str, replacement: str, results: List[ReplacementResult]):
        """Commit changes to Git if repository exists."""
        try:
            # Check if we're in a Git repository
            subprocess.run(['git', 'status'], cwd=self.root_path, capture_output=True, check=True)

            # Add changed files
            changed_files = [r.file_path for r in results if r.success]
            if changed_files:
                subprocess.run(['git', 'add'] + changed_files, cwd=self.root_path, check=True)

                # Create commit message
                commit_msg = f"Smart replace: '{search_pattern}' -> '{replacement}'"
                subprocess.run(['git', 'commit', '-m', commit_msg], cwd=self.root_path, check=True)

                logger.info(f"Committed changes to Git: {len(changed_files)} files")

        except subprocess.CalledProcessError:
            logger.warning("Git commit failed or not in a Git repository")
        except Exception as e:
            logger.error(f"Error during Git commit: {e}")

    def format_preview(self, previews: List[ReplacementPreview]) -> str:
        """Format replacement previews for display."""
        if not previews:
            return "No changes to preview."

        output = []
        output.append(f"Preview of {len(previews)} potential changes:")
        output.append("=" * 60)

        current_file = None
        for preview in previews:
            if preview.file_path != current_file:
                current_file = preview.file_path
                output.append(f"\nFile: {current_file}")
                output.append("-" * 40)

            output.append(f"Line {preview.line_number}:")

            # Show context
            for line in preview.context_before:
                output.append(f"  {line}")

            output.append(f"- {preview.original_line}")
            output.append(f"+ {preview.new_line}")

            for line in preview.context_after:
                output.append(f"  {line}")

            output.append("")

        return "\n".join(output)
