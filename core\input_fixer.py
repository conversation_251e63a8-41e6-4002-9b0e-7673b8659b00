#!/usr/bin/env python3

"""
Input Fixer Module for CODY Agent
Provides code fixing capabilities for malformed input, syntax errors, 
and formatting issues across multiple programming languages.
"""

import os
import re
import ast
import json
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
import logging
import tempfile

# Third-party imports with fallbacks
try:
    import black
    BLACK_AVAILABLE = True
except ImportError:
    BLACK_AVAILABLE = False

try:
    import autopep8
    AUTOPEP8_AVAILABLE = True
except ImportError:
    AUTOPEP8_AVAILABLE = False

try:
    import isort
    ISORT_AVAILABLE = True
except ImportError:
    ISORT_AVAILABLE = False

logger = logging.getLogger('CODY.InputFixer')

class FixType(Enum):
    """Types of fixes that can be applied."""
    SYNTAX_ERROR = "syntax_error"
    INDENTATION = "indentation"
    FORMATTING = "formatting"
    IMPORTS = "imports"
    BRACKETS = "brackets"
    QUOTES = "quotes"
    ENCODING = "encoding"
    WHITESPACE = "whitespace"
    SEMICOLONS = "semicolons"
    TRAILING_COMMAS = "trailing_commas"

class Language(Enum):
    """Supported programming languages."""
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    JAVA = "java"
    CPP = "cpp"
    C = "c"
    JSON = "json"
    HTML = "html"
    CSS = "css"
    SQL = "sql"
    BASH = "bash"
    UNKNOWN = "unknown"

@dataclass
class FixResult:
    """Result of a code fixing operation."""
    original_code: str
    fixed_code: str
    language: Language
    fixes_applied: List[FixType] = field(default_factory=list)
    success: bool = True
    error_message: Optional[str] = None
    warnings: List[str] = field(default_factory=list)
    suggestions: List[str] = field(default_factory=list)
    confidence: float = 1.0

@dataclass
class SyntaxError:
    """Information about a syntax error."""
    line_number: int
    column: int
    message: str
    error_type: str
    suggested_fix: Optional[str] = None

class InputFixer:
    """
    Advanced input fixer for code with multi-language support and intelligent error detection.
    """
    
    def __init__(self):
        # Language detection patterns
        self.language_patterns = {
            Language.PYTHON: [
                r'def\s+\w+\s*\(',
                r'import\s+\w+',
                r'from\s+\w+\s+import',
                r'class\s+\w+\s*[\(:]',
                r'if\s+__name__\s*==\s*[\'"]__main__[\'"]',
                r'print\s*\(',
                r'#.*python'
            ],
            Language.JAVASCRIPT: [
                r'function\s+\w+\s*\(',
                r'var\s+\w+\s*=',
                r'let\s+\w+\s*=',
                r'const\s+\w+\s*=',
                r'console\.log\s*\(',
                r'=>',
                r'//.*javascript'
            ],
            Language.JAVA: [
                r'public\s+class\s+\w+',
                r'public\s+static\s+void\s+main',
                r'System\.out\.print',
                r'import\s+java\.',
                r'@\w+',
                r'//.*java'
            ],
            Language.JSON: [
                r'^\s*\{',
                r'^\s*\[',
                r':\s*["\d\[\{]',
                r'"[^"]*"\s*:'
            ]
        }
        
        # Common syntax error patterns and fixes
        self.syntax_fixes = {
            Language.PYTHON: {
                r'(\w+)\s*=\s*\{([^}]+)\}': r'\1 = {\2}',  # Dict spacing
                r'(\w+)\s*=\s*\[([^\]]+)\]': r'\1 = [\2]',  # List spacing
                r'if\s+(.+):$': r'if \1:',  # If statement
                r'for\s+(.+):$': r'for \1:',  # For loop
                r'while\s+(.+):$': r'while \1:',  # While loop
                r'def\s+(\w+)\s*\(([^)]*)\):$': r'def \1(\2):',  # Function def
                r'class\s+(\w+)\s*:$': r'class \1:',  # Class def
            },
            Language.JAVASCRIPT: {
                r'function\s+(\w+)\s*\(([^)]*)\)\s*\{': r'function \1(\2) {',
                r'if\s*\(([^)]+)\)\s*\{': r'if (\1) {',
                r'for\s*\(([^)]+)\)\s*\{': r'for (\1) {',
                r'while\s*\(([^)]+)\)\s*\{': r'while (\1) {',
            }
        }
        
        # Bracket matching patterns
        self.bracket_pairs = {
            '(': ')',
            '[': ']',
            '{': '}',
            '"': '"',
            "'": "'"
        }

    def fix_code(self, code: str, language: Optional[Language] = None,
                 fix_types: Optional[List[FixType]] = None) -> FixResult:
        """
        Fix malformed code with intelligent error detection and correction.
        
        Args:
            code: The code to fix
            language: Programming language (auto-detected if None)
            fix_types: Specific types of fixes to apply (all if None)
            
        Returns:
            FixResult with the fixed code and applied fixes
        """
        if not code.strip():
            return FixResult(
                original_code=code,
                fixed_code=code,
                language=Language.UNKNOWN,
                success=False,
                error_message="Empty code provided"
            )
        
        # Detect language if not provided
        if language is None:
            language = self._detect_language(code)
        
        # Initialize result
        result = FixResult(
            original_code=code,
            fixed_code=code,
            language=language
        )
        
        try:
            # Apply fixes based on language and requested types
            if fix_types is None:
                fix_types = list(FixType)
            
            fixed_code = code
            
            # Apply fixes in order of importance
            if FixType.ENCODING in fix_types:
                fixed_code, encoding_applied = self._fix_encoding(fixed_code)
                if encoding_applied:
                    result.fixes_applied.append(FixType.ENCODING)
            
            if FixType.BRACKETS in fix_types:
                fixed_code, brackets_applied = self._fix_brackets(fixed_code)
                if brackets_applied:
                    result.fixes_applied.append(FixType.BRACKETS)
            
            if FixType.QUOTES in fix_types:
                fixed_code, quotes_applied = self._fix_quotes(fixed_code)
                if quotes_applied:
                    result.fixes_applied.append(FixType.QUOTES)
            
            if FixType.INDENTATION in fix_types:
                fixed_code, indent_applied = self._fix_indentation(fixed_code, language)
                if indent_applied:
                    result.fixes_applied.append(FixType.INDENTATION)
            
            if FixType.SYNTAX_ERROR in fix_types:
                fixed_code, syntax_applied = self._fix_syntax_errors(fixed_code, language)
                if syntax_applied:
                    result.fixes_applied.append(FixType.SYNTAX_ERROR)
            
            if FixType.WHITESPACE in fix_types:
                fixed_code, whitespace_applied = self._fix_whitespace(fixed_code)
                if whitespace_applied:
                    result.fixes_applied.append(FixType.WHITESPACE)
            
            if FixType.IMPORTS in fix_types and language == Language.PYTHON:
                fixed_code, imports_applied = self._fix_imports(fixed_code)
                if imports_applied:
                    result.fixes_applied.append(FixType.IMPORTS)
            
            if FixType.FORMATTING in fix_types:
                fixed_code, formatting_applied = self._fix_formatting(fixed_code, language)
                if formatting_applied:
                    result.fixes_applied.append(FixType.FORMATTING)
            
            # Validate the fixed code
            validation_result = self._validate_code(fixed_code, language)
            if not validation_result['valid']:
                result.warnings.append(f"Fixed code may still have issues: {validation_result['error']}")
                result.confidence = 0.7
            
            result.fixed_code = fixed_code
            result.success = True
            
            # Generate suggestions
            result.suggestions = self._generate_suggestions(code, fixed_code, language)
            
        except Exception as e:
            result.success = False
            result.error_message = str(e)
            logger.error(f"Error fixing code: {e}")
        
        return result

    def _detect_language(self, code: str) -> Language:
        """Detect the programming language of the code."""
        code_lower = code.lower()
        
        # Check for explicit language indicators
        if '#!/usr/bin/env python' in code or '#!/usr/bin/python' in code:
            return Language.PYTHON
        elif '#!/bin/bash' in code or '#!/bin/sh' in code:
            return Language.BASH
        
        # Check file extension patterns
        if code.strip().startswith('{') or code.strip().startswith('['):
            try:
                json.loads(code)
                return Language.JSON
            except:
                pass
        
        # Check language-specific patterns
        scores = {}
        for lang, patterns in self.language_patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, code, re.IGNORECASE | re.MULTILINE))
                score += matches
            scores[lang] = score
        
        # Return language with highest score
        if scores:
            best_lang = max(scores, key=scores.get)
            if scores[best_lang] > 0:
                return best_lang
        
        return Language.UNKNOWN

    def _fix_encoding(self, code: str) -> Tuple[str, bool]:
        """Fix encoding issues in the code."""
        fixed = False

        # Remove BOM if present
        if code.startswith('\ufeff'):
            code = code[1:]
            fixed = True

        # Fix common encoding issues
        replacements = {
            '\u2018': "'",  # Left single quotation mark
            '\u2019': "'",  # Right single quotation mark
            '\u201c': '"',  # Left double quotation mark
            '\u201d': '"',  # Right double quotation mark
            '\u2013': '-',  # En dash
            '\u2014': '--', # Em dash
            '\u2026': '...', # Horizontal ellipsis
        }

        for bad_char, good_char in replacements.items():
            if bad_char in code:
                code = code.replace(bad_char, good_char)
                fixed = True

        return code, fixed

    def _fix_brackets(self, code: str) -> Tuple[str, bool]:
        """Fix unmatched brackets, parentheses, and braces."""
        lines = code.splitlines()
        fixed_lines = []
        fixed = False

        for line in lines:
            fixed_line = line

            # Count brackets
            bracket_counts = {}
            for open_bracket, close_bracket in self.bracket_pairs.items():
                if open_bracket == close_bracket:  # Quotes
                    count = line.count(open_bracket)
                    if count % 2 != 0:  # Odd number of quotes
                        fixed_line += open_bracket
                        fixed = True
                else:  # Brackets and parentheses
                    open_count = line.count(open_bracket)
                    close_count = line.count(close_bracket)

                    if open_count > close_count:
                        fixed_line += close_bracket * (open_count - close_count)
                        fixed = True
                    elif close_count > open_count:
                        fixed_line = open_bracket * (close_count - open_count) + fixed_line
                        fixed = True

            fixed_lines.append(fixed_line)

        return '\n'.join(fixed_lines), fixed

    def _fix_quotes(self, code: str) -> Tuple[str, bool]:
        """Fix quote-related issues."""
        fixed = False

        # Fix mixed quotes in strings
        lines = code.splitlines()
        fixed_lines = []

        for line in lines:
            # Simple heuristic: if line has both single and double quotes,
            # prefer double quotes for strings
            if "'" in line and '"' in line:
                # Count quotes to determine which to use
                single_count = line.count("'")
                double_count = line.count('"')

                # If more single quotes, convert some to double
                if single_count > double_count and single_count % 2 == 0:
                    # Simple replacement for string literals
                    line = re.sub(r"'([^']*)'", r'"\1"', line)
                    fixed = True

            fixed_lines.append(line)

        return '\n'.join(fixed_lines), fixed

    def _fix_indentation(self, code: str, language: Language) -> Tuple[str, bool]:
        """Fix indentation issues."""
        if language not in [Language.PYTHON, Language.YAML]:
            return code, False

        lines = code.splitlines()
        if not lines:
            return code, False

        fixed_lines = []
        fixed = False

        # Detect indentation style
        indent_sizes = []
        for line in lines:
            if line.strip() and line.startswith(' '):
                leading_spaces = len(line) - len(line.lstrip(' '))
                if leading_spaces > 0:
                    indent_sizes.append(leading_spaces)

        # Determine most common indentation
        if indent_sizes:
            from collections import Counter
            common_indent = Counter(indent_sizes).most_common(1)[0][0]
        else:
            common_indent = 4  # Default to 4 spaces

        # Fix inconsistent indentation
        for line in lines:
            if line.strip():
                leading_spaces = len(line) - len(line.lstrip(' '))
                if leading_spaces > 0 and leading_spaces % common_indent != 0:
                    # Round to nearest multiple of common_indent
                    new_indent = round(leading_spaces / common_indent) * common_indent
                    fixed_line = ' ' * new_indent + line.lstrip(' ')
                    fixed_lines.append(fixed_line)
                    fixed = True
                else:
                    fixed_lines.append(line)
            else:
                fixed_lines.append(line)

        return '\n'.join(fixed_lines), fixed

    def _fix_syntax_errors(self, code: str, language: Language) -> Tuple[str, bool]:
        """Fix common syntax errors."""
        if language not in self.syntax_fixes:
            return code, False

        fixed_code = code
        fixed = False

        patterns = self.syntax_fixes[language]
        for pattern, replacement in patterns.items():
            new_code = re.sub(pattern, replacement, fixed_code, flags=re.MULTILINE)
            if new_code != fixed_code:
                fixed_code = new_code
                fixed = True

        # Language-specific fixes
        if language == Language.PYTHON:
            fixed_code, python_fixed = self._fix_python_syntax(fixed_code)
            fixed = fixed or python_fixed
        elif language == Language.JAVASCRIPT:
            fixed_code, js_fixed = self._fix_javascript_syntax(fixed_code)
            fixed = fixed or js_fixed

        return fixed_code, fixed

    def _fix_python_syntax(self, code: str) -> Tuple[str, bool]:
        """Fix Python-specific syntax errors."""
        fixed = False
        lines = code.splitlines()
        fixed_lines = []

        for line in lines:
            fixed_line = line

            # Fix missing colons
            if re.match(r'^\s*(if|elif|else|for|while|def|class|try|except|finally|with)\s+.*[^:]$', line.strip()):
                if not line.strip().endswith(':'):
                    fixed_line = line + ':'
                    fixed = True

            # Fix print statements (Python 2 to 3)
            if 'print ' in line and not 'print(' in line:
                fixed_line = re.sub(r'print\s+(.+)', r'print(\1)', fixed_line)
                fixed = True

            fixed_lines.append(fixed_line)

        return '\n'.join(fixed_lines), fixed

    def _fix_javascript_syntax(self, code: str) -> Tuple[str, bool]:
        """Fix JavaScript-specific syntax errors."""
        fixed = False

        # Fix missing semicolons
        lines = code.splitlines()
        fixed_lines = []

        for line in lines:
            stripped = line.strip()
            if (stripped and
                not stripped.endswith((';', '{', '}', ':', ',')) and
                not stripped.startswith(('if', 'for', 'while', 'function', 'class', '//', '/*')) and
                not stripped.endswith('*/') and
                '=' in stripped or 'return' in stripped):
                fixed_lines.append(line + ';')
                fixed = True
            else:
                fixed_lines.append(line)

        return '\n'.join(fixed_lines), fixed

    def _fix_whitespace(self, code: str) -> Tuple[str, bool]:
        """Fix whitespace issues."""
        original_code = code

        # Remove trailing whitespace
        lines = code.splitlines()
        fixed_lines = [line.rstrip() for line in lines]

        # Remove excessive blank lines (more than 2 consecutive)
        final_lines = []
        blank_count = 0

        for line in fixed_lines:
            if line.strip() == '':
                blank_count += 1
                if blank_count <= 2:
                    final_lines.append(line)
            else:
                blank_count = 0
                final_lines.append(line)

        # Ensure file ends with single newline
        while final_lines and final_lines[-1] == '':
            final_lines.pop()

        fixed_code = '\n'.join(final_lines)
        if not fixed_code.endswith('\n') and fixed_code:
            fixed_code += '\n'

        return fixed_code, fixed_code != original_code

    def _fix_imports(self, code: str) -> Tuple[str, bool]:
        """Fix Python import statements."""
        if not ISORT_AVAILABLE:
            return code, False

        try:
            fixed_code = isort.code(code)
            return fixed_code, fixed_code != code
        except Exception as e:
            logger.debug(f"isort failed: {e}")
            return code, False

    def _fix_formatting(self, code: str, language: Language) -> Tuple[str, bool]:
        """Apply language-specific formatting."""
        if language == Language.PYTHON:
            return self._fix_python_formatting(code)
        elif language == Language.JAVASCRIPT:
            return self._fix_javascript_formatting(code)
        elif language == Language.JSON:
            return self._fix_json_formatting(code)
        else:
            return code, False

    def _fix_python_formatting(self, code: str) -> Tuple[str, bool]:
        """Apply Python formatting using black or autopep8."""
        # Try black first
        if BLACK_AVAILABLE:
            try:
                fixed_code = black.format_str(code, mode=black.FileMode())
                return fixed_code, fixed_code != code
            except Exception as e:
                logger.debug(f"Black formatting failed: {e}")

        # Fallback to autopep8
        if AUTOPEP8_AVAILABLE:
            try:
                fixed_code = autopep8.fix_code(code)
                return fixed_code, fixed_code != code
            except Exception as e:
                logger.debug(f"autopep8 formatting failed: {e}")

        return code, False

    def _fix_javascript_formatting(self, code: str) -> Tuple[str, bool]:
        """Apply basic JavaScript formatting."""
        # Basic formatting rules
        lines = code.splitlines()
        fixed_lines = []
        indent_level = 0

        for line in lines:
            stripped = line.strip()
            if not stripped:
                fixed_lines.append('')
                continue

            # Decrease indent for closing braces
            if stripped.startswith('}'):
                indent_level = max(0, indent_level - 1)

            # Add proper indentation
            fixed_line = '  ' * indent_level + stripped
            fixed_lines.append(fixed_line)

            # Increase indent for opening braces
            if stripped.endswith('{'):
                indent_level += 1

        fixed_code = '\n'.join(fixed_lines)
        return fixed_code, fixed_code != code

    def _fix_json_formatting(self, code: str) -> Tuple[str, bool]:
        """Fix JSON formatting."""
        try:
            # Parse and reformat JSON
            parsed = json.loads(code)
            fixed_code = json.dumps(parsed, indent=2, ensure_ascii=False)
            return fixed_code, fixed_code != code
        except json.JSONDecodeError:
            return code, False

    def _validate_code(self, code: str, language: Language) -> Dict[str, Any]:
        """Validate the fixed code."""
        if language == Language.PYTHON:
            return self._validate_python(code)
        elif language == Language.JSON:
            return self._validate_json(code)
        else:
            return {'valid': True, 'error': None}

    def _validate_python(self, code: str) -> Dict[str, Any]:
        """Validate Python code syntax."""
        try:
            ast.parse(code)
            return {'valid': True, 'error': None}
        except SyntaxError as e:
            return {
                'valid': False,
                'error': f"Syntax error at line {e.lineno}: {e.msg}"
            }
        except Exception as e:
            return {
                'valid': False,
                'error': f"Validation error: {str(e)}"
            }

    def _validate_json(self, code: str) -> Dict[str, Any]:
        """Validate JSON syntax."""
        try:
            json.loads(code)
            return {'valid': True, 'error': None}
        except json.JSONDecodeError as e:
            return {
                'valid': False,
                'error': f"JSON error at line {e.lineno}: {e.msg}"
            }

    def _generate_suggestions(self, original: str, fixed: str, language: Language) -> List[str]:
        """Generate suggestions for code improvement."""
        suggestions = []

        if language == Language.PYTHON:
            # Check for common Python improvements
            if 'import *' in original:
                suggestions.append("Consider avoiding 'import *' and use specific imports instead")

            if re.search(r'except:', original):
                suggestions.append("Consider catching specific exceptions instead of bare 'except:'")

            if 'print(' in original and 'logging' not in original:
                suggestions.append("Consider using logging instead of print statements for better debugging")

        elif language == Language.JAVASCRIPT:
            if 'var ' in original:
                suggestions.append("Consider using 'let' or 'const' instead of 'var'")

            if '==' in original and '===' not in original:
                suggestions.append("Consider using '===' for strict equality comparison")

        # General suggestions
        if len(original.splitlines()) > 50:
            suggestions.append("Consider breaking this into smaller functions or modules")

        if original != fixed:
            suggestions.append("Code has been automatically fixed. Please review the changes.")

        return suggestions

    def format_result(self, result: FixResult) -> str:
        """Format the fix result for display."""
        output = []

        if result.success:
            output.append("✅ CODE FIXING COMPLETED")
            output.append("=" * 50)
            output.append(f"Language: {result.language.value}")
            output.append(f"Confidence: {result.confidence:.1%}")

            if result.fixes_applied:
                output.append(f"\n🔧 FIXES APPLIED:")
                for fix_type in result.fixes_applied:
                    output.append(f"   • {fix_type.value.replace('_', ' ').title()}")
            else:
                output.append("\n✨ No fixes needed - code looks good!")

            if result.warnings:
                output.append(f"\n⚠️ WARNINGS:")
                for warning in result.warnings:
                    output.append(f"   • {warning}")

            if result.suggestions:
                output.append(f"\n💡 SUGGESTIONS:")
                for suggestion in result.suggestions:
                    output.append(f"   • {suggestion}")

            # Show code diff if changes were made
            if result.original_code != result.fixed_code:
                output.append(f"\n📝 FIXED CODE:")
                output.append("-" * 40)
                output.append(result.fixed_code)
                output.append("-" * 40)
        else:
            output.append("❌ CODE FIXING FAILED")
            output.append("=" * 50)
            output.append(f"Error: {result.error_message}")
            output.append(f"\n📝 ORIGINAL CODE:")
            output.append("-" * 40)
            output.append(result.original_code)
            output.append("-" * 40)

        return "\n".join(output)
