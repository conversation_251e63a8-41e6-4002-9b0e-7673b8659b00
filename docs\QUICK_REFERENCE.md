# CODY Advanced AI Coding Tools - Quick Reference

## 🔍 Advanced Pattern Search
**Function**: `advanced_pattern_search()`

**Purpose**: Semantic understanding and complex pattern matching across codebases

**Key Parameters**:
- `query`: Search query (natural language, regex, or specific pattern)
- `search_mode`: "semantic", "fuzzy", "regex", "natural_language", "hybrid"
- `file_types`: File extensions to search (e.g., ['.py', '.js'])
- `max_results`: Maximum number of results (default: 50)
- `context_lines`: Number of context lines around matches (default: 3)
- `similarity_threshold`: Similarity threshold for fuzzy matching (default: 0.8)

**Quick Examples**:
```python
# Find all model-related code
advanced_pattern_search("gemini model initialization", "semantic", [".py"])

# Complex regex search
advanced_pattern_search("gemini.*model|GEMINI.*MODEL", "regex")

# Natural language search
advanced_pattern_search("find all authentication functions", "natural_language")
```

---

## 🔧 Smart Code Replacement
**Function**: `smart_replace_code()`

**Purpose**: Intelligent string and code replacement with preview, Git integration, and complex refactoring

**Key Parameters**:
- `search_pattern`: Pattern to search for
- `replacement`: Replacement text or code
- `replacement_mode`: "simple", "regex", "function_signature", "variable_rename"
- `preview_only`: Show preview without making changes (default: true)
- `backup_files`: Create backup files before modification (default: true)
- `git_commit`: Automatically commit changes to Git (default: false)
- `file_paths`: Specific files to modify (optional)

**Quick Examples**:
```python
# Preview function name changes
smart_replace_code("old_func", "new_func", replacement_mode="function_signature", preview_only=True)

# Apply changes with Git commit
smart_replace_code("deprecated_api", "new_api", preview_only=False, git_commit=True)

# Regex replacement
smart_replace_code(r"print\s*\(([^)]+)\)", r"logger.info(\1)", replacement_mode="regex")
```

---

## 🔎 Smart File Search (grep++)
**Function**: `smart_file_search()`

**Purpose**: Advanced grep-like content search with code context understanding and semantic ranking

**Key Parameters**:
- `query`: Search query
- `search_type`: "text", "regex", "function", "class", "variable", "import", "comment", "semantic", "fuzzy"
- `file_patterns`: File patterns to include (e.g., ['*.py', '*.js'])
- `max_results`: Maximum number of results (default: 100)
- `include_context`: Whether to include code context (default: true)
- `context_lines`: Number of context lines (default: 3)
- `case_sensitive`: Whether search is case sensitive (default: false)

**Quick Examples**:
```python
# Find function definitions
smart_file_search("authenticate", "function", include_context=True)

# Semantic search
smart_file_search("database connection pooling", "semantic")

# Search imports
smart_file_search("flask", "import")
```

---

## 🤖 Auto Planner
**Functions**: `create_execution_plan()`, `execute_plan()`, `list_execution_plans()`

**Purpose**: Autonomous task decomposition and execution with progress tracking and Git integration

**Key Parameters for create_execution_plan**:
- `user_request`: Natural language description of what to accomplish
- `strategy`: "waterfall", "agile", "incremental", "parallel"
- `auto_execute`: Whether to automatically execute the plan (default: false)
- `auto_commit`: Whether to automatically commit changes to Git (default: false)
- `context`: Additional context information for planning

**Quick Examples**:
```python
# Create a development plan
create_execution_plan("Add user authentication with JWT tokens", "waterfall")

# Execute a plan
execute_plan("plan_1234567890", auto_commit=True)

# List all plans
list_execution_plans()
```

---

## 🔧 Input Fixer
**Function**: `fix_code_input()`

**Purpose**: Automatic code fixing for malformed input, syntax errors, and formatting issues

**Key Parameters**:
- `code`: The code to fix
- `language`: Programming language ("python", "javascript", "java", etc.) - auto-detected if not specified
- `fix_types`: Specific types of fixes to apply (optional)
  - Available: "syntax_error", "indentation", "formatting", "imports", "brackets", "quotes", "encoding", "whitespace"

**Quick Examples**:
```python
# Fix Python syntax errors
fix_code_input('def broken_function(\n    print "Hello"', "python")

# Fix JavaScript issues
fix_code_input('function test( {\n    console.log("hi")\n}', "javascript")

# Auto-detect and fix all issues
fix_code_input(malformed_code)
```

---

## 📚 File Indexing & Codebase Context
**Functions**: `build_file_index()`, `search_file_index()`, `get_index_stats()`

**Purpose**: Efficient indexing system for large files and codebases with semantic understanding

**Key Parameters for build_file_index**:
- `force_rebuild`: Whether to rebuild the entire index (default: false)

**Key Parameters for search_file_index**:
- `query`: Search query
- `search_type`: "full_text", "semantic", "structural", "metadata"
- `max_results`: Maximum number of results (default: 50)
- `file_types`: Filter by file types ("source_code", "documentation", "configuration", etc.)

**Quick Examples**:
```python
# Build index
build_file_index(force_rebuild=False)

# Semantic search
search_file_index("authentication middleware", "semantic", file_types=["source_code"])

# Get statistics
get_index_stats()
```

---

## 🚀 Combined Workflow Examples

### Complete Refactoring Workflow:
```python
# 1. Build index
build_file_index()

# 2. Find relevant code
results = advanced_pattern_search("authentication", "semantic")

# 3. Preview changes
smart_replace_code("old_auth", "new_auth", preview_only=True)

# 4. Create execution plan
plan = create_execution_plan("Refactor authentication system")

# 5. Execute
execute_plan(plan.id, auto_commit=True)
```

### Bug Investigation Workflow:
```python
# 1. Search for error patterns
errors = smart_file_search("NullPointerException|TypeError", "regex")

# 2. Fix syntax issues
for error in errors:
    fix_code_input(error.content)

# 3. Create comprehensive fix plan
create_execution_plan("Fix all null pointer exceptions", auto_execute=True)
```

### New Feature Development:
```python
# 1. Research existing patterns
patterns = search_file_index("rate limiting", "semantic")

# 2. Create development plan
plan = create_execution_plan("Implement API rate limiting")

# 3. Execute with monitoring
execute_plan(plan.id, auto_commit=True)
```

---

## 💡 Best Practices

1. **Always start with indexing**: `build_file_index()` for better search results
2. **Use preview mode first**: `preview_only=True` before applying changes
3. **Enable backups**: `backup_files=True` for safety
4. **Use semantic search**: More intelligent than simple text search
5. **Combine tools**: Use multiple tools together for complex workflows
6. **Check index stats**: `get_index_stats()` to understand your codebase
7. **Use hybrid search**: Best of all search modes combined
8. **Create execution plans**: For complex multi-step tasks
9. **Enable Git integration**: `git_commit=True` for change tracking
10. **Use context**: `include_context=True` for better understanding

---

## 🎯 When to Use Each Tool

- **Advanced Pattern Search**: Finding complex patterns, exploring unfamiliar codebases
- **Smart Code Replacement**: Refactoring, renaming, updating APIs
- **Smart File Search**: Quick content search, finding specific code elements
- **Auto Planner**: Complex development tasks, systematic code changes
- **Input Fixer**: Cleaning up malformed code, fixing syntax errors
- **File Indexing**: Large codebases, semantic understanding, fast navigation

---

## 🔧 Troubleshooting

**No search results?**
- Check if index is built: `get_index_stats()`
- Try different search modes
- Verify file types and patterns

**Slow performance?**
- Rebuild index: `build_file_index(force_rebuild=True)`
- Reduce search scope with file filters
- Check available memory

**Code fixing fails?**
- Verify language detection
- Try specific fix types
- Check for unsupported syntax

**Plan execution errors?**
- Review dependencies and task order
- Check file permissions
- Verify Git status
