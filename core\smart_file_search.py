#!/usr/bin/env python3

"""
Smart File Search Module for CODY Agent
Advanced grep-like content search with code context understanding, 
syntax awareness, and semantic ranking.
"""

import os
import re
import ast
import json
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
import logging
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

# Third-party imports with fallbacks
try:
    from thefuzz import fuzz, process as fuzzy_process
    FUZZY_AVAILABLE = True
except ImportError:
    FUZZY_AVAILABLE = False

try:
    import tree_sitter
    from tree_sitter import Language, Parser
    TREE_SITTER_AVAILABLE = True
except ImportError:
    TREE_SITTER_AVAILABLE = False

logger = logging.getLogger('CODY.SmartFileSearch')

class SearchType(Enum):
    """Types of smart file search."""
    TEXT = "text"
    REGEX = "regex"
    FUNCTION = "function"
    CLASS = "class"
    VARIABLE = "variable"
    IMPORT = "import"
    COMMENT = "comment"
    SEMANTIC = "semantic"
    FUZZY = "fuzzy"

class ContextType(Enum):
    """Types of code context."""
    FUNCTION_BODY = "function_body"
    CLASS_BODY = "class_body"
    MODULE_LEVEL = "module_level"
    CONDITIONAL = "conditional"
    LOOP = "loop"
    TRY_EXCEPT = "try_except"

@dataclass
class SmartSearchResult:
    """Enhanced search result with code context and semantic information."""
    file_path: str
    line_number: int
    line_content: str
    match_text: str
    search_type: SearchType
    relevance_score: float
    context_type: ContextType
    context_before: List[str] = field(default_factory=list)
    context_after: List[str] = field(default_factory=list)
    function_name: Optional[str] = None
    class_name: Optional[str] = None
    variable_names: List[str] = field(default_factory=list)
    imports: List[str] = field(default_factory=list)
    syntax_highlighting: Dict[str, Any] = field(default_factory=dict)
    semantic_tags: List[str] = field(default_factory=list)
    code_complexity: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class SearchStatistics:
    """Statistics for search operations."""
    files_searched: int = 0
    total_matches: int = 0
    search_time: float = 0.0
    excluded_files: int = 0
    error_files: int = 0
    average_relevance: float = 0.0
    top_file_types: List[Tuple[str, int]] = field(default_factory=list)

class SmartFileSearchEngine:
    """
    Advanced file search engine with code context understanding and semantic ranking.
    """
    
    def __init__(self, root_path: str = "."):
        self.root_path = Path(root_path)
        self.excluded_extensions = {
            '.pyc', '.pyo', '.pyd', '.so', '.dll', '.dylib', '.exe', '.bin',
            '.png', '.jpg', '.jpeg', '.gif', '.ico', '.svg', '.webp',
            '.mp4', '.webm', '.mov', '.mp3', '.wav', '.ogg',
            '.zip', '.tar', '.gz', '.7z', '.rar', '.pdf', '.doc', '.docx'
        }
        self.excluded_dirs = {
            '__pycache__', '.git', '.svn', '.hg', 'node_modules', '.venv', 'venv',
            '.pytest_cache', '.mypy_cache', 'dist', 'build', '.cache', '.idea', '.vscode'
        }
        
        # Language-specific patterns for context-aware search
        self.language_patterns = {
            'python': {
                'function': r'def\s+(\w+)\s*\(',
                'class': r'class\s+(\w+)\s*[\(:]',
                'import': r'(?:from\s+[\w.]+\s+)?import\s+([^\n]+)',
                'variable': r'(\w+)\s*=\s*[^=]',
                'decorator': r'@(\w+)',
                'comment': r'#\s*(.+)',
                'docstring': r'"""([^"]+)"""',
            },
            'javascript': {
                'function': r'(?:function\s+(\w+)|(\w+)\s*=\s*(?:function|\([^)]*\)\s*=>))',
                'class': r'class\s+(\w+)',
                'import': r'import\s+.*?from\s+[\'"]([^\'"]+)[\'"]',
                'variable': r'(?:let|const|var)\s+(\w+)',
                'comment': r'//\s*(.+)',
            },
            'java': {
                'function': r'(?:public|private|protected)?\s*(?:static)?\s*\w+\s+(\w+)\s*\(',
                'class': r'(?:public|private)?\s*class\s+(\w+)',
                'import': r'import\s+([^;]+);',
                'variable': r'\w+\s+(\w+)\s*=',
                'comment': r'//\s*(.+)',
            }
        }
        
        # Semantic keywords for intelligent ranking
        self.semantic_keywords = {
            'high_priority': ['main', 'init', 'setup', 'config', 'error', 'exception', 'critical'],
            'medium_priority': ['process', 'handle', 'manage', 'create', 'update', 'delete'],
            'low_priority': ['helper', 'util', 'temp', 'test', 'debug', 'log']
        }

    def search(self, query: str, search_type: SearchType = SearchType.TEXT,
               file_patterns: Optional[List[str]] = None, max_results: int = 100,
               include_context: bool = True, context_lines: int = 3,
               case_sensitive: bool = False, whole_words: bool = False,
               include_hidden: bool = False) -> Tuple[List[SmartSearchResult], SearchStatistics]:
        """
        Perform smart file search with context understanding and semantic ranking.
        
        Args:
            query: Search query
            search_type: Type of search to perform
            file_patterns: File patterns to include (e.g., ['*.py', '*.js'])
            max_results: Maximum number of results
            include_context: Whether to include code context
            context_lines: Number of context lines
            case_sensitive: Whether search is case sensitive
            whole_words: Whether to match whole words only
            include_hidden: Whether to include hidden files
            
        Returns:
            Tuple of (search results, search statistics)
        """
        start_time = time.time()
        stats = SearchStatistics()
        
        # Get files to search
        files_to_search = self._get_files_to_search(file_patterns, include_hidden)
        stats.files_searched = len(files_to_search)
        
        # Perform search based on type
        all_results = []
        
        if search_type == SearchType.TEXT:
            all_results = self._text_search(query, files_to_search, case_sensitive, whole_words)
        elif search_type == SearchType.REGEX:
            all_results = self._regex_search(query, files_to_search)
        elif search_type == SearchType.FUNCTION:
            all_results = self._function_search(query, files_to_search, case_sensitive)
        elif search_type == SearchType.CLASS:
            all_results = self._class_search(query, files_to_search, case_sensitive)
        elif search_type == SearchType.VARIABLE:
            all_results = self._variable_search(query, files_to_search, case_sensitive)
        elif search_type == SearchType.IMPORT:
            all_results = self._import_search(query, files_to_search, case_sensitive)
        elif search_type == SearchType.COMMENT:
            all_results = self._comment_search(query, files_to_search, case_sensitive)
        elif search_type == SearchType.SEMANTIC:
            all_results = self._semantic_search(query, files_to_search)
        elif search_type == SearchType.FUZZY:
            all_results = self._fuzzy_search(query, files_to_search)
        
        # Add context information if requested
        if include_context:
            all_results = self._add_context_information(all_results, context_lines)
        
        # Calculate relevance scores and rank results
        all_results = self._calculate_relevance_scores(all_results, query)
        all_results.sort(key=lambda x: x.relevance_score, reverse=True)
        
        # Limit results
        results = all_results[:max_results]
        
        # Calculate statistics
        stats.total_matches = len(results)
        stats.search_time = time.time() - start_time
        if results:
            stats.average_relevance = sum(r.relevance_score for r in results) / len(results)
        
        # Calculate top file types
        file_type_counts = {}
        for result in results:
            ext = Path(result.file_path).suffix.lower()
            file_type_counts[ext] = file_type_counts.get(ext, 0) + 1
        stats.top_file_types = sorted(file_type_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return results, stats

    def _get_files_to_search(self, file_patterns: Optional[List[str]], include_hidden: bool) -> List[Path]:
        """Get list of files to search based on patterns."""
        files = []
        
        for file_path in self.root_path.rglob('*'):
            if not file_path.is_file():
                continue
                
            # Skip hidden files unless requested
            if not include_hidden and any(part.startswith('.') for part in file_path.parts):
                continue
                
            # Skip excluded directories
            if any(excluded_dir in file_path.parts for excluded_dir in self.excluded_dirs):
                continue
                
            # Skip excluded extensions
            if file_path.suffix.lower() in self.excluded_extensions:
                continue
                
            # Filter by file patterns if specified
            if file_patterns:
                if not any(file_path.match(pattern) for pattern in file_patterns):
                    continue
            
            files.append(file_path)
        
        return files

    def _text_search(self, query: str, files: List[Path], case_sensitive: bool, whole_words: bool) -> List[SmartSearchResult]:
        """Perform basic text search."""
        results = []

        # Prepare search pattern
        search_query = query if case_sensitive else query.lower()
        if whole_words:
            pattern = re.compile(r'\b' + re.escape(search_query) + r'\b',
                               re.IGNORECASE if not case_sensitive else 0)

        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()

                for i, line in enumerate(lines):
                    search_line = line if case_sensitive else line.lower()

                    if whole_words:
                        if pattern.search(line):
                            match_text = pattern.search(line).group()
                        else:
                            continue
                    else:
                        if search_query in search_line:
                            match_text = query
                        else:
                            continue

                    result = SmartSearchResult(
                        file_path=str(file_path),
                        line_number=i + 1,
                        line_content=line.strip(),
                        match_text=match_text,
                        search_type=SearchType.TEXT,
                        relevance_score=0.5,  # Base score, will be calculated later
                        context_type=ContextType.MODULE_LEVEL
                    )
                    results.append(result)

            except Exception as e:
                logger.debug(f"Error searching file {file_path}: {e}")
                continue

        return results

    def _regex_search(self, query: str, files: List[Path]) -> List[SmartSearchResult]:
        """Perform regex-based search."""
        results = []

        try:
            pattern = re.compile(query, re.MULTILINE)
        except re.error as e:
            logger.error(f"Invalid regex pattern: {query}, error: {e}")
            return []

        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    lines = content.splitlines()

                for match in pattern.finditer(content):
                    line_num = content[:match.start()].count('\n') + 1
                    line_content = lines[line_num - 1] if line_num <= len(lines) else ""

                    result = SmartSearchResult(
                        file_path=str(file_path),
                        line_number=line_num,
                        line_content=line_content.strip(),
                        match_text=match.group(),
                        search_type=SearchType.REGEX,
                        relevance_score=0.7,  # Higher score for regex matches
                        context_type=ContextType.MODULE_LEVEL
                    )
                    results.append(result)

            except Exception as e:
                logger.debug(f"Error in regex search for file {file_path}: {e}")
                continue

        return results

    def _function_search(self, query: str, files: List[Path], case_sensitive: bool) -> List[SmartSearchResult]:
        """Search for function definitions."""
        results = []

        for file_path in files:
            try:
                language = self._detect_language(file_path)
                if language not in self.language_patterns:
                    continue

                function_pattern = self.language_patterns[language].get('function')
                if not function_pattern:
                    continue

                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    lines = content.splitlines()

                pattern = re.compile(function_pattern, re.MULTILINE)
                for match in pattern.finditer(content):
                    function_name = match.group(1) if match.groups() else match.group()

                    # Check if query matches function name
                    if case_sensitive:
                        if query not in function_name:
                            continue
                    else:
                        if query.lower() not in function_name.lower():
                            continue

                    line_num = content[:match.start()].count('\n') + 1
                    line_content = lines[line_num - 1] if line_num <= len(lines) else ""

                    result = SmartSearchResult(
                        file_path=str(file_path),
                        line_number=line_num,
                        line_content=line_content.strip(),
                        match_text=function_name,
                        search_type=SearchType.FUNCTION,
                        relevance_score=0.8,  # High score for function matches
                        context_type=ContextType.FUNCTION_BODY,
                        function_name=function_name,
                        semantic_tags=['function_definition']
                    )
                    results.append(result)

            except Exception as e:
                logger.debug(f"Error in function search for file {file_path}: {e}")
                continue

        return results

    def _class_search(self, query: str, files: List[Path], case_sensitive: bool) -> List[SmartSearchResult]:
        """Search for class definitions."""
        results = []

        for file_path in files:
            try:
                language = self._detect_language(file_path)
                if language not in self.language_patterns:
                    continue

                class_pattern = self.language_patterns[language].get('class')
                if not class_pattern:
                    continue

                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    lines = content.splitlines()

                pattern = re.compile(class_pattern, re.MULTILINE)
                for match in pattern.finditer(content):
                    class_name = match.group(1) if match.groups() else match.group()

                    # Check if query matches class name
                    if case_sensitive:
                        if query not in class_name:
                            continue
                    else:
                        if query.lower() not in class_name.lower():
                            continue

                    line_num = content[:match.start()].count('\n') + 1
                    line_content = lines[line_num - 1] if line_num <= len(lines) else ""

                    result = SmartSearchResult(
                        file_path=str(file_path),
                        line_number=line_num,
                        line_content=line_content.strip(),
                        match_text=class_name,
                        search_type=SearchType.CLASS,
                        relevance_score=0.8,  # High score for class matches
                        context_type=ContextType.CLASS_BODY,
                        class_name=class_name,
                        semantic_tags=['class_definition']
                    )
                    results.append(result)

            except Exception as e:
                logger.debug(f"Error in class search for file {file_path}: {e}")
                continue

        return results

    def _variable_search(self, query: str, files: List[Path], case_sensitive: bool) -> List[SmartSearchResult]:
        """Search for variable assignments."""
        results = []

        for file_path in files:
            try:
                language = self._detect_language(file_path)
                if language not in self.language_patterns:
                    continue

                variable_pattern = self.language_patterns[language].get('variable')
                if not variable_pattern:
                    continue

                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    lines = content.splitlines()

                pattern = re.compile(variable_pattern, re.MULTILINE)
                for match in pattern.finditer(content):
                    variable_name = match.group(1) if match.groups() else match.group()

                    # Check if query matches variable name
                    if case_sensitive:
                        if query not in variable_name:
                            continue
                    else:
                        if query.lower() not in variable_name.lower():
                            continue

                    line_num = content[:match.start()].count('\n') + 1
                    line_content = lines[line_num - 1] if line_num <= len(lines) else ""

                    result = SmartSearchResult(
                        file_path=str(file_path),
                        line_number=line_num,
                        line_content=line_content.strip(),
                        match_text=variable_name,
                        search_type=SearchType.VARIABLE,
                        relevance_score=0.6,
                        context_type=ContextType.MODULE_LEVEL,
                        variable_names=[variable_name],
                        semantic_tags=['variable_assignment']
                    )
                    results.append(result)

            except Exception as e:
                logger.debug(f"Error in variable search for file {file_path}: {e}")
                continue

        return results

    def _import_search(self, query: str, files: List[Path], case_sensitive: bool) -> List[SmartSearchResult]:
        """Search for import statements."""
        results = []

        for file_path in files:
            try:
                language = self._detect_language(file_path)
                if language not in self.language_patterns:
                    continue

                import_pattern = self.language_patterns[language].get('import')
                if not import_pattern:
                    continue

                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    lines = content.splitlines()

                pattern = re.compile(import_pattern, re.MULTILINE)
                for match in pattern.finditer(content):
                    import_text = match.group(1) if match.groups() else match.group()

                    # Check if query matches import
                    if case_sensitive:
                        if query not in import_text:
                            continue
                    else:
                        if query.lower() not in import_text.lower():
                            continue

                    line_num = content[:match.start()].count('\n') + 1
                    line_content = lines[line_num - 1] if line_num <= len(lines) else ""

                    result = SmartSearchResult(
                        file_path=str(file_path),
                        line_number=line_num,
                        line_content=line_content.strip(),
                        match_text=import_text,
                        search_type=SearchType.IMPORT,
                        relevance_score=0.7,
                        context_type=ContextType.MODULE_LEVEL,
                        imports=[import_text],
                        semantic_tags=['import_statement']
                    )
                    results.append(result)

            except Exception as e:
                logger.debug(f"Error in import search for file {file_path}: {e}")
                continue

        return results

    def _comment_search(self, query: str, files: List[Path], case_sensitive: bool) -> List[SmartSearchResult]:
        """Search for comments."""
        results = []

        for file_path in files:
            try:
                language = self._detect_language(file_path)
                if language not in self.language_patterns:
                    continue

                comment_pattern = self.language_patterns[language].get('comment')
                if not comment_pattern:
                    continue

                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    lines = content.splitlines()

                pattern = re.compile(comment_pattern, re.MULTILINE)
                for match in pattern.finditer(content):
                    comment_text = match.group(1) if match.groups() else match.group()

                    # Check if query matches comment
                    if case_sensitive:
                        if query not in comment_text:
                            continue
                    else:
                        if query.lower() not in comment_text.lower():
                            continue

                    line_num = content[:match.start()].count('\n') + 1
                    line_content = lines[line_num - 1] if line_num <= len(lines) else ""

                    result = SmartSearchResult(
                        file_path=str(file_path),
                        line_number=line_num,
                        line_content=line_content.strip(),
                        match_text=comment_text,
                        search_type=SearchType.COMMENT,
                        relevance_score=0.4,  # Lower score for comments
                        context_type=ContextType.MODULE_LEVEL,
                        semantic_tags=['comment']
                    )
                    results.append(result)

            except Exception as e:
                logger.debug(f"Error in comment search for file {file_path}: {e}")
                continue

        return results

    def _semantic_search(self, query: str, files: List[Path]) -> List[SmartSearchResult]:
        """Perform semantic search using keyword analysis."""
        results = []

        # Extract keywords from query
        query_keywords = query.lower().split()

        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    lines = content.splitlines()

                for i, line in enumerate(lines):
                    line_lower = line.lower()

                    # Calculate semantic relevance
                    relevance = 0.0
                    matched_keywords = []

                    for keyword in query_keywords:
                        if keyword in line_lower:
                            matched_keywords.append(keyword)

                            # Boost score based on keyword priority
                            if keyword in self.semantic_keywords['high_priority']:
                                relevance += 0.3
                            elif keyword in self.semantic_keywords['medium_priority']:
                                relevance += 0.2
                            else:
                                relevance += 0.1

                    if matched_keywords:
                        result = SmartSearchResult(
                            file_path=str(file_path),
                            line_number=i + 1,
                            line_content=line.strip(),
                            match_text=' '.join(matched_keywords),
                            search_type=SearchType.SEMANTIC,
                            relevance_score=relevance,
                            context_type=ContextType.MODULE_LEVEL,
                            semantic_tags=['semantic_match'] + matched_keywords
                        )
                        results.append(result)

            except Exception as e:
                logger.debug(f"Error in semantic search for file {file_path}: {e}")
                continue

        return results

    def _fuzzy_search(self, query: str, files: List[Path]) -> List[SmartSearchResult]:
        """Perform fuzzy search using string similarity."""
        if not FUZZY_AVAILABLE:
            logger.warning("Fuzzy search not available. Install thefuzz package.")
            return []

        results = []

        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()

                for i, line in enumerate(lines):
                    # Calculate fuzzy similarity
                    similarity = fuzz.partial_ratio(query.lower(), line.lower()) / 100.0

                    if similarity >= 0.6:  # Threshold for fuzzy matches
                        result = SmartSearchResult(
                            file_path=str(file_path),
                            line_number=i + 1,
                            line_content=line.strip(),
                            match_text=line.strip(),
                            search_type=SearchType.FUZZY,
                            relevance_score=similarity,
                            context_type=ContextType.MODULE_LEVEL,
                            semantic_tags=['fuzzy_match']
                        )
                        results.append(result)

            except Exception as e:
                logger.debug(f"Error in fuzzy search for file {file_path}: {e}")
                continue

        return results

    def _detect_language(self, file_path: Path) -> str:
        """Detect programming language from file extension."""
        extension = file_path.suffix.lower()
        language_map = {
            '.py': 'python',
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.ts': 'javascript',
            '.tsx': 'javascript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'cpp',
            '.h': 'cpp',
            '.hpp': 'cpp',
            '.go': 'go',
            '.rs': 'rust',
            '.php': 'php',
            '.rb': 'ruby',
            '.cs': 'csharp',
            '.swift': 'swift',
            '.kt': 'kotlin'
        }
        return language_map.get(extension, 'unknown')

    def _add_context_information(self, results: List[SmartSearchResult], context_lines: int) -> List[SmartSearchResult]:
        """Add context information to search results."""
        for result in results:
            try:
                with open(result.file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()

                line_index = result.line_number - 1

                # Get context before
                start_before = max(0, line_index - context_lines)
                result.context_before = [line.strip() for line in lines[start_before:line_index]]

                # Get context after
                end_after = min(len(lines), line_index + 1 + context_lines)
                result.context_after = [line.strip() for line in lines[line_index + 1:end_after]]

                # Determine context type based on surrounding code
                result.context_type = self._determine_context_type(lines, line_index)

            except Exception as e:
                logger.debug(f"Error adding context for {result.file_path}: {e}")
                continue

        return results

    def _determine_context_type(self, lines: List[str], line_index: int) -> ContextType:
        """Determine the context type of a line based on surrounding code."""
        # Simple heuristic-based context detection
        context_window = 5
        start = max(0, line_index - context_window)
        end = min(len(lines), line_index + context_window + 1)

        context_lines = [line.strip() for line in lines[start:end]]
        context_text = ' '.join(context_lines).lower()

        if any(keyword in context_text for keyword in ['def ', 'function ']):
            return ContextType.FUNCTION_BODY
        elif any(keyword in context_text for keyword in ['class ']):
            return ContextType.CLASS_BODY
        elif any(keyword in context_text for keyword in ['if ', 'elif ', 'else:']):
            return ContextType.CONDITIONAL
        elif any(keyword in context_text for keyword in ['for ', 'while ']):
            return ContextType.LOOP
        elif any(keyword in context_text for keyword in ['try:', 'except:', 'finally:']):
            return ContextType.TRY_EXCEPT
        else:
            return ContextType.MODULE_LEVEL

    def _calculate_relevance_scores(self, results: List[SmartSearchResult], query: str) -> List[SmartSearchResult]:
        """Calculate and update relevance scores for search results."""
        query_lower = query.lower()

        for result in results:
            base_score = result.relevance_score

            # Boost score based on search type
            type_boost = {
                SearchType.FUNCTION: 0.3,
                SearchType.CLASS: 0.3,
                SearchType.IMPORT: 0.2,
                SearchType.VARIABLE: 0.1,
                SearchType.COMMENT: -0.1,
                SearchType.TEXT: 0.0,
                SearchType.REGEX: 0.2,
                SearchType.SEMANTIC: 0.1,
                SearchType.FUZZY: -0.1
            }
            base_score += type_boost.get(result.search_type, 0.0)

            # Boost score based on file type
            file_ext = Path(result.file_path).suffix.lower()
            if file_ext in ['.py', '.js', '.java', '.cpp']:
                base_score += 0.1

            # Boost score based on filename relevance
            filename = Path(result.file_path).stem.lower()
            if query_lower in filename:
                base_score += 0.2

            # Boost score based on line content relevance
            line_lower = result.line_content.lower()
            query_words = query_lower.split()
            word_matches = sum(1 for word in query_words if word in line_lower)
            base_score += (word_matches / len(query_words)) * 0.2

            # Boost score for exact matches
            if query_lower in line_lower:
                base_score += 0.1

            # Normalize score to 0-1 range
            result.relevance_score = min(1.0, max(0.0, base_score))

        return results

    def format_results(self, results: List[SmartSearchResult], stats: SearchStatistics,
                      show_context: bool = True, max_context_lines: int = 2) -> str:
        """Format search results for display."""
        if not results:
            return f"No matches found. Searched {stats.files_searched} files in {stats.search_time:.2f}s"

        output = []
        output.append(f"Found {len(results)} matches in {stats.files_searched} files ({stats.search_time:.2f}s)")
        output.append(f"Average relevance: {stats.average_relevance:.2f}")

        if stats.top_file_types:
            file_types = ", ".join([f"{ext}({count})" for ext, count in stats.top_file_types])
            output.append(f"Top file types: {file_types}")

        output.append("=" * 80)

        for i, result in enumerate(results, 1):
            output.append(f"\n{i}. {result.file_path}:{result.line_number}")
            output.append(f"   Type: {result.search_type.value} | Relevance: {result.relevance_score:.2f}")
            output.append(f"   Context: {result.context_type.value}")

            if result.semantic_tags:
                output.append(f"   Tags: {', '.join(result.semantic_tags)}")

            # Show context
            if show_context and result.context_before:
                output.append("   Context before:")
                for line in result.context_before[-max_context_lines:]:
                    output.append(f"     {line}")

            output.append(f"   >>> {result.line_content}")

            if show_context and result.context_after:
                output.append("   Context after:")
                for line in result.context_after[:max_context_lines]:
                    output.append(f"     {line}")

            if i < len(results):
                output.append("-" * 40)

        return "\n".join(output)
