#!/usr/bin/env python3

"""
File Indexing and Codebase Context Module for CODY Agent
Implements efficient indexing system for large files and codebases with 
semantic understanding and real-time updates.
"""

import os
import json
import time
import hashlib
import sqlite3
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
import logging
import threading
from concurrent.futures import ThreadPoolExecutor
import pickle

# Third-party imports with fallbacks
try:
    import watchdog
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
    WATCHDOG_AVAILABLE = True
except ImportError:
    WATCHDOG_AVAILABLE = False

try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    import numpy as np
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

logger = logging.getLogger('CODY.FileIndexer')

class IndexType(Enum):
    """Types of file indexing."""
    FULL_TEXT = "full_text"
    SEMANTIC = "semantic"
    STRUCTURAL = "structural"
    METADATA = "metadata"

class FileType(Enum):
    """Categorized file types."""
    SOURCE_CODE = "source_code"
    DOCUMENTATION = "documentation"
    CONFIGURATION = "configuration"
    DATA = "data"
    BINARY = "binary"
    UNKNOWN = "unknown"

@dataclass
class FileIndex:
    """Index entry for a single file."""
    file_path: str
    file_hash: str
    file_size: int
    file_type: FileType
    language: Optional[str]
    last_modified: float
    last_indexed: float
    content_hash: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # Structural information
    functions: List[Dict[str, Any]] = field(default_factory=list)
    classes: List[Dict[str, Any]] = field(default_factory=list)
    imports: List[str] = field(default_factory=list)
    variables: List[str] = field(default_factory=list)
    
    # Semantic information
    keywords: List[str] = field(default_factory=list)
    topics: List[str] = field(default_factory=list)
    complexity_score: float = 0.0
    
    # Full text index
    content_tokens: List[str] = field(default_factory=list)
    line_count: int = 0

@dataclass
class SearchResult:
    """Result from index search."""
    file_path: str
    relevance_score: float
    match_type: str
    matched_content: List[str] = field(default_factory=list)
    line_numbers: List[int] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)

class FileIndexer:
    """
    Advanced file indexing system with semantic understanding and real-time updates.
    """
    
    def __init__(self, root_path: str = ".", index_dir: str = ".cody_index"):
        self.root_path = Path(root_path)
        self.index_dir = self.root_path / index_dir
        self.index_dir.mkdir(exist_ok=True)
        
        # Database for storing index
        self.db_path = self.index_dir / "file_index.db"
        self.init_database()
        
        # In-memory cache for fast access
        self.index_cache: Dict[str, FileIndex] = {}
        self.cache_lock = threading.RLock()
        
        # File watching
        self.observer = None
        self.watching = False
        
        # Semantic analysis
        self.tfidf_vectorizer = None
        self.document_vectors = None
        
        # Configuration
        self.excluded_extensions = {
            '.pyc', '.pyo', '.pyd', '.so', '.dll', '.dylib', '.exe', '.bin',
            '.png', '.jpg', '.jpeg', '.gif', '.ico', '.svg', '.webp',
            '.mp4', '.webm', '.mov', '.mp3', '.wav', '.ogg',
            '.zip', '.tar', '.gz', '.7z', '.rar'
        }
        
        self.excluded_dirs = {
            '__pycache__', '.git', '.svn', '.hg', 'node_modules', '.venv', 'venv',
            '.pytest_cache', '.mypy_cache', 'dist', 'build', '.cache', '.idea', '.vscode'
        }
        
        # Language detection patterns
        self.language_patterns = {
            '.py': 'python',
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.ts': 'typescript',
            '.tsx': 'typescript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.h': 'c',
            '.hpp': 'cpp',
            '.go': 'go',
            '.rs': 'rust',
            '.php': 'php',
            '.rb': 'ruby',
            '.cs': 'csharp',
            '.swift': 'swift',
            '.kt': 'kotlin',
            '.html': 'html',
            '.css': 'css',
            '.json': 'json',
            '.xml': 'xml',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.md': 'markdown',
            '.txt': 'text',
            '.sql': 'sql',
            '.sh': 'bash',
            '.bat': 'batch'
        }

    def init_database(self):
        """Initialize the SQLite database for storing index."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS file_index (
                    file_path TEXT PRIMARY KEY,
                    file_hash TEXT,
                    file_size INTEGER,
                    file_type TEXT,
                    language TEXT,
                    last_modified REAL,
                    last_indexed REAL,
                    content_hash TEXT,
                    metadata TEXT,
                    functions TEXT,
                    classes TEXT,
                    imports TEXT,
                    variables TEXT,
                    keywords TEXT,
                    topics TEXT,
                    complexity_score REAL,
                    content_tokens TEXT,
                    line_count INTEGER
                )
            ''')
            
            conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_file_type ON file_index(file_type)
            ''')
            
            conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_language ON file_index(language)
            ''')
            
            conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_last_modified ON file_index(last_modified)
            ''')

    def build_index(self, force_rebuild: bool = False, 
                   progress_callback: Optional[callable] = None) -> Dict[str, Any]:
        """
        Build or update the file index.
        
        Args:
            force_rebuild: Whether to rebuild the entire index
            progress_callback: Optional callback for progress updates
            
        Returns:
            Index building statistics
        """
        start_time = time.time()
        stats = {
            'files_processed': 0,
            'files_updated': 0,
            'files_skipped': 0,
            'errors': 0,
            'total_time': 0.0
        }
        
        try:
            # Load existing index if not force rebuilding
            if not force_rebuild:
                self.load_index_cache()
            
            # Get all files to index
            files_to_process = self._get_files_to_index()
            total_files = len(files_to_process)
            
            if progress_callback:
                progress_callback(f"Found {total_files} files to process")
            
            # Process files in batches
            batch_size = 100
            with ThreadPoolExecutor(max_workers=4) as executor:
                for i in range(0, total_files, batch_size):
                    batch = files_to_process[i:i + batch_size]
                    
                    # Submit batch for processing
                    futures = []
                    for file_path in batch:
                        future = executor.submit(self._index_file, file_path, force_rebuild)
                        futures.append((future, file_path))
                    
                    # Collect results
                    for future, file_path in futures:
                        try:
                            result = future.result()
                            if result:
                                if result['updated']:
                                    stats['files_updated'] += 1
                                else:
                                    stats['files_skipped'] += 1
                            stats['files_processed'] += 1
                            
                            if progress_callback and stats['files_processed'] % 50 == 0:
                                progress = (stats['files_processed'] / total_files) * 100
                                progress_callback(f"Progress: {progress:.1f}% ({stats['files_processed']}/{total_files})")
                                
                        except Exception as e:
                            logger.error(f"Error indexing {file_path}: {e}")
                            stats['errors'] += 1
            
            # Build semantic index if sklearn is available
            if SKLEARN_AVAILABLE:
                self._build_semantic_index()
            
            # Save index to database
            self._save_index_to_db()
            
            stats['total_time'] = time.time() - start_time
            
            if progress_callback:
                progress_callback(f"Index building completed in {stats['total_time']:.2f}s")
            
        except Exception as e:
            logger.error(f"Error building index: {e}")
            stats['errors'] += 1
        
        return stats

    def _get_files_to_index(self) -> List[Path]:
        """Get list of files that should be indexed."""
        files = []

        for file_path in self.root_path.rglob('*'):
            if not file_path.is_file():
                continue

            # Skip excluded directories
            if any(excluded_dir in file_path.parts for excluded_dir in self.excluded_dirs):
                continue

            # Skip excluded extensions
            if file_path.suffix.lower() in self.excluded_extensions:
                continue

            # Skip very large files (>10MB)
            try:
                if file_path.stat().st_size > 10 * 1024 * 1024:
                    continue
            except OSError:
                continue

            files.append(file_path)

        return files

    def _index_file(self, file_path: Path, force_rebuild: bool = False) -> Optional[Dict[str, Any]]:
        """Index a single file."""
        try:
            # Get file stats
            stat = file_path.stat()
            file_hash = self._calculate_file_hash(file_path)

            # Check if file needs reindexing
            with self.cache_lock:
                existing_index = self.index_cache.get(str(file_path))
                if (not force_rebuild and existing_index and
                    existing_index.file_hash == file_hash and
                    existing_index.last_modified >= stat.st_mtime):
                    return {'updated': False}

            # Read file content
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
            except Exception:
                # Try binary mode for non-text files
                return {'updated': False}

            # Determine file type and language
            file_type = self._determine_file_type(file_path, content)
            language = self._detect_language(file_path, content)

            # Create file index
            file_index = FileIndex(
                file_path=str(file_path),
                file_hash=file_hash,
                file_size=stat.st_size,
                file_type=file_type,
                language=language,
                last_modified=stat.st_mtime,
                last_indexed=time.time(),
                content_hash=hashlib.md5(content.encode()).hexdigest(),
                line_count=len(content.splitlines())
            )

            # Extract structural information
            if file_type == FileType.SOURCE_CODE:
                self._extract_structural_info(file_index, content)

            # Extract semantic information
            self._extract_semantic_info(file_index, content)

            # Tokenize content for full-text search
            file_index.content_tokens = self._tokenize_content(content)

            # Store in cache
            with self.cache_lock:
                self.index_cache[str(file_path)] = file_index

            return {'updated': True}

        except Exception as e:
            logger.error(f"Error indexing file {file_path}: {e}")
            return None

    def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate hash of file for change detection."""
        try:
            stat = file_path.stat()
            # Use file size and modification time for quick hash
            hash_input = f"{file_path}:{stat.st_size}:{stat.st_mtime}"
            return hashlib.md5(hash_input.encode()).hexdigest()
        except Exception:
            return ""

    def _determine_file_type(self, file_path: Path, content: str) -> FileType:
        """Determine the type of file."""
        extension = file_path.suffix.lower()

        # Source code files
        if extension in {'.py', '.js', '.jsx', '.ts', '.tsx', '.java', '.cpp', '.c', '.h', '.hpp',
                        '.go', '.rs', '.php', '.rb', '.cs', '.swift', '.kt'}:
            return FileType.SOURCE_CODE

        # Documentation files
        if extension in {'.md', '.txt', '.rst', '.doc', '.docx', '.pdf'}:
            return FileType.DOCUMENTATION

        # Configuration files
        if extension in {'.json', '.yaml', '.yml', '.xml', '.ini', '.cfg', '.conf', '.toml'}:
            return FileType.CONFIGURATION

        # Data files
        if extension in {'.csv', '.tsv', '.sql', '.db', '.sqlite'}:
            return FileType.DATA

        # Binary files
        if extension in self.excluded_extensions:
            return FileType.BINARY

        # Check content for text files
        try:
            content.encode('utf-8')
            return FileType.UNKNOWN
        except UnicodeEncodeError:
            return FileType.BINARY

    def _detect_language(self, file_path: Path, content: str) -> Optional[str]:
        """Detect programming language of the file."""
        extension = file_path.suffix.lower()
        return self.language_patterns.get(extension)

    def _extract_structural_info(self, file_index: FileIndex, content: str):
        """Extract structural information from source code."""
        if not file_index.language:
            return

        lines = content.splitlines()

        # Language-specific extraction
        if file_index.language == 'python':
            self._extract_python_structure(file_index, content, lines)
        elif file_index.language in ['javascript', 'typescript']:
            self._extract_javascript_structure(file_index, content, lines)
        elif file_index.language == 'java':
            self._extract_java_structure(file_index, content, lines)

    def _extract_python_structure(self, file_index: FileIndex, content: str, lines: List[str]):
        """Extract Python-specific structural information."""
        import re

        # Extract functions
        for i, line in enumerate(lines):
            # Function definitions
            func_match = re.match(r'^\s*def\s+(\w+)\s*\(([^)]*)\):', line)
            if func_match:
                file_index.functions.append({
                    'name': func_match.group(1),
                    'parameters': func_match.group(2),
                    'line_number': i + 1,
                    'type': 'function'
                })

            # Class definitions
            class_match = re.match(r'^\s*class\s+(\w+)\s*[\(:]', line)
            if class_match:
                file_index.classes.append({
                    'name': class_match.group(1),
                    'line_number': i + 1,
                    'type': 'class'
                })

            # Import statements
            import_match = re.match(r'^\s*(?:from\s+[\w.]+\s+)?import\s+([^\n]+)', line)
            if import_match:
                file_index.imports.append(import_match.group(1).strip())

            # Variable assignments (simple heuristic)
            var_match = re.match(r'^\s*(\w+)\s*=\s*[^=]', line)
            if var_match and not line.strip().startswith('#'):
                file_index.variables.append(var_match.group(1))

    def _extract_javascript_structure(self, file_index: FileIndex, content: str, lines: List[str]):
        """Extract JavaScript/TypeScript structural information."""
        import re

        for i, line in enumerate(lines):
            # Function definitions
            func_match = re.match(r'^\s*(?:function\s+(\w+)|(\w+)\s*=\s*(?:function|\([^)]*\)\s*=>))', line)
            if func_match:
                name = func_match.group(1) or func_match.group(2)
                file_index.functions.append({
                    'name': name,
                    'line_number': i + 1,
                    'type': 'function'
                })

            # Class definitions
            class_match = re.match(r'^\s*class\s+(\w+)', line)
            if class_match:
                file_index.classes.append({
                    'name': class_match.group(1),
                    'line_number': i + 1,
                    'type': 'class'
                })

            # Import statements
            import_match = re.match(r'^\s*import\s+.*?from\s+[\'"]([^\'"]+)[\'"]', line)
            if import_match:
                file_index.imports.append(import_match.group(1))

    def _extract_java_structure(self, file_index: FileIndex, content: str, lines: List[str]):
        """Extract Java structural information."""
        import re

        for i, line in enumerate(lines):
            # Method definitions
            method_match = re.match(r'^\s*(?:public|private|protected)?\s*(?:static)?\s*\w+\s+(\w+)\s*\(', line)
            if method_match:
                file_index.functions.append({
                    'name': method_match.group(1),
                    'line_number': i + 1,
                    'type': 'method'
                })

            # Class definitions
            class_match = re.match(r'^\s*(?:public|private)?\s*class\s+(\w+)', line)
            if class_match:
                file_index.classes.append({
                    'name': class_match.group(1),
                    'line_number': i + 1,
                    'type': 'class'
                })

            # Import statements
            import_match = re.match(r'^\s*import\s+([^;]+);', line)
            if import_match:
                file_index.imports.append(import_match.group(1).strip())

    def _extract_semantic_info(self, file_index: FileIndex, content: str):
        """Extract semantic information from content."""
        import re

        # Extract keywords (simple approach)
        words = re.findall(r'\b[a-zA-Z_][a-zA-Z0-9_]*\b', content.lower())

        # Filter common words and keep meaningful keywords
        common_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        keywords = [word for word in set(words) if len(word) > 2 and word not in common_words]

        # Keep top keywords by frequency
        from collections import Counter
        keyword_counts = Counter(words)
        file_index.keywords = [word for word, count in keyword_counts.most_common(20)
                              if word in keywords]

        # Calculate complexity score (simple heuristic)
        lines = content.splitlines()
        non_empty_lines = [line for line in lines if line.strip()]

        if non_empty_lines:
            avg_line_length = sum(len(line) for line in non_empty_lines) / len(non_empty_lines)
            complexity_factors = [
                len(file_index.functions) * 0.1,
                len(file_index.classes) * 0.2,
                avg_line_length / 100,
                len(non_empty_lines) / 1000
            ]
            file_index.complexity_score = min(1.0, sum(complexity_factors))

    def _tokenize_content(self, content: str) -> List[str]:
        """Tokenize content for full-text search."""
        import re

        # Simple tokenization
        tokens = re.findall(r'\b[a-zA-Z_][a-zA-Z0-9_]*\b', content.lower())

        # Remove duplicates while preserving order
        seen = set()
        unique_tokens = []
        for token in tokens:
            if token not in seen and len(token) > 2:
                seen.add(token)
                unique_tokens.append(token)

        return unique_tokens[:1000]  # Limit to 1000 tokens

    def _build_semantic_index(self):
        """Build semantic index using TF-IDF."""
        if not SKLEARN_AVAILABLE:
            return

        try:
            # Collect all documents
            documents = []
            file_paths = []

            with self.cache_lock:
                for file_path, file_index in self.index_cache.items():
                    if file_index.content_tokens:
                        documents.append(' '.join(file_index.content_tokens))
                        file_paths.append(file_path)

            if not documents:
                return

            # Build TF-IDF vectors
            self.tfidf_vectorizer = TfidfVectorizer(
                max_features=5000,
                stop_words='english',
                ngram_range=(1, 2)
            )

            self.document_vectors = self.tfidf_vectorizer.fit_transform(documents)

            # Store file path mapping
            self.vector_file_mapping = {i: file_paths[i] for i in range(len(file_paths))}

        except Exception as e:
            logger.error(f"Error building semantic index: {e}")

    def search(self, query: str, search_type: IndexType = IndexType.FULL_TEXT,
               max_results: int = 50, file_types: Optional[List[FileType]] = None) -> List[SearchResult]:
        """
        Search the file index.

        Args:
            query: Search query
            search_type: Type of search to perform
            max_results: Maximum number of results
            file_types: Filter by file types

        Returns:
            List of search results
        """
        results = []

        try:
            if search_type == IndexType.SEMANTIC and SKLEARN_AVAILABLE and self.tfidf_vectorizer:
                results = self._semantic_search(query, max_results, file_types)
            elif search_type == IndexType.STRUCTURAL:
                results = self._structural_search(query, max_results, file_types)
            else:
                results = self._full_text_search(query, max_results, file_types)

        except Exception as e:
            logger.error(f"Error searching index: {e}")

        return results

    def _full_text_search(self, query: str, max_results: int,
                         file_types: Optional[List[FileType]]) -> List[SearchResult]:
        """Perform full-text search."""
        results = []
        query_tokens = query.lower().split()

        with self.cache_lock:
            for file_path, file_index in self.index_cache.items():
                if file_types and file_index.file_type not in file_types:
                    continue

                # Calculate relevance score
                score = 0.0
                matches = []

                # Check content tokens
                for token in query_tokens:
                    if token in file_index.content_tokens:
                        score += 1.0
                        matches.append(token)

                # Check keywords
                for token in query_tokens:
                    if token in file_index.keywords:
                        score += 2.0  # Higher weight for keywords
                        matches.append(token)

                # Check file path
                if any(token in file_path.lower() for token in query_tokens):
                    score += 0.5

                if score > 0:
                    results.append(SearchResult(
                        file_path=file_path,
                        relevance_score=score,
                        match_type="full_text",
                        matched_content=matches
                    ))

        # Sort by relevance and limit results
        results.sort(key=lambda x: x.relevance_score, reverse=True)
        return results[:max_results]

    def _semantic_search(self, query: str, max_results: int,
                        file_types: Optional[List[FileType]]) -> List[SearchResult]:
        """Perform semantic search using TF-IDF."""
        if not self.tfidf_vectorizer or not SKLEARN_AVAILABLE:
            return []

        try:
            # Transform query
            query_vector = self.tfidf_vectorizer.transform([query])

            # Calculate similarities
            similarities = cosine_similarity(query_vector, self.document_vectors).flatten()

            # Get top results
            top_indices = np.argsort(similarities)[::-1][:max_results * 2]  # Get more for filtering

            results = []
            for idx in top_indices:
                if similarities[idx] > 0.1:  # Minimum similarity threshold
                    file_path = self.vector_file_mapping[idx]

                    with self.cache_lock:
                        file_index = self.index_cache.get(file_path)
                        if file_index and (not file_types or file_index.file_type in file_types):
                            results.append(SearchResult(
                                file_path=file_path,
                                relevance_score=similarities[idx],
                                match_type="semantic"
                            ))

            return results[:max_results]

        except Exception as e:
            logger.error(f"Error in semantic search: {e}")
            return []

    def _structural_search(self, query: str, max_results: int,
                          file_types: Optional[List[FileType]]) -> List[SearchResult]:
        """Search for structural elements (functions, classes, etc.)."""
        results = []
        query_lower = query.lower()

        with self.cache_lock:
            for file_path, file_index in self.index_cache.items():
                if file_types and file_index.file_type not in file_types:
                    continue

                score = 0.0
                matches = []

                # Search functions
                for func in file_index.functions:
                    if query_lower in func['name'].lower():
                        score += 3.0
                        matches.append(f"function: {func['name']}")

                # Search classes
                for cls in file_index.classes:
                    if query_lower in cls['name'].lower():
                        score += 3.0
                        matches.append(f"class: {cls['name']}")

                # Search imports
                for imp in file_index.imports:
                    if query_lower in imp.lower():
                        score += 1.0
                        matches.append(f"import: {imp}")

                if score > 0:
                    results.append(SearchResult(
                        file_path=file_path,
                        relevance_score=score,
                        match_type="structural",
                        matched_content=matches
                    ))

        results.sort(key=lambda x: x.relevance_score, reverse=True)
        return results[:max_results]

    def load_index_cache(self):
        """Load index from database into memory cache."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute('SELECT * FROM file_index')

                with self.cache_lock:
                    self.index_cache.clear()

                    for row in cursor:
                        file_index = FileIndex(
                            file_path=row[0],
                            file_hash=row[1],
                            file_size=row[2],
                            file_type=FileType(row[3]),
                            language=row[4],
                            last_modified=row[5],
                            last_indexed=row[6],
                            content_hash=row[7],
                            metadata=json.loads(row[8]) if row[8] else {},
                            functions=json.loads(row[9]) if row[9] else [],
                            classes=json.loads(row[10]) if row[10] else [],
                            imports=json.loads(row[11]) if row[11] else [],
                            variables=json.loads(row[12]) if row[12] else [],
                            keywords=json.loads(row[13]) if row[13] else [],
                            topics=json.loads(row[14]) if row[14] else [],
                            complexity_score=row[15] or 0.0,
                            content_tokens=json.loads(row[16]) if row[16] else [],
                            line_count=row[17] or 0
                        )
                        self.index_cache[row[0]] = file_index

        except Exception as e:
            logger.error(f"Error loading index cache: {e}")

    def _save_index_to_db(self):
        """Save index cache to database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Clear existing data
                conn.execute('DELETE FROM file_index')

                # Insert current cache
                with self.cache_lock:
                    for file_path, file_index in self.index_cache.items():
                        conn.execute('''
                            INSERT INTO file_index VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            file_index.file_path,
                            file_index.file_hash,
                            file_index.file_size,
                            file_index.file_type.value,
                            file_index.language,
                            file_index.last_modified,
                            file_index.last_indexed,
                            file_index.content_hash,
                            json.dumps(file_index.metadata),
                            json.dumps(file_index.functions),
                            json.dumps(file_index.classes),
                            json.dumps(file_index.imports),
                            json.dumps(file_index.variables),
                            json.dumps(file_index.keywords),
                            json.dumps(file_index.topics),
                            file_index.complexity_score,
                            json.dumps(file_index.content_tokens),
                            file_index.line_count
                        ))

                conn.commit()

        except Exception as e:
            logger.error(f"Error saving index to database: {e}")

    def get_index_stats(self) -> Dict[str, Any]:
        """Get statistics about the current index."""
        with self.cache_lock:
            total_files = len(self.index_cache)

            if total_files == 0:
                return {
                    'total_files': 0,
                    'file_types': {},
                    'languages': {},
                    'total_size': 0,
                    'total_lines': 0,
                    'last_updated': None
                }

            file_types = {}
            languages = {}
            total_size = 0
            total_lines = 0
            last_updated = 0

            for file_index in self.index_cache.values():
                # File types
                file_type = file_index.file_type.value
                file_types[file_type] = file_types.get(file_type, 0) + 1

                # Languages
                if file_index.language:
                    languages[file_index.language] = languages.get(file_index.language, 0) + 1

                # Totals
                total_size += file_index.file_size
                total_lines += file_index.line_count
                last_updated = max(last_updated, file_index.last_indexed)

            return {
                'total_files': total_files,
                'file_types': file_types,
                'languages': languages,
                'total_size': total_size,
                'total_lines': total_lines,
                'last_updated': last_updated,
                'semantic_index_available': self.tfidf_vectorizer is not None
            }

    def start_watching(self):
        """Start watching for file changes."""
        if not WATCHDOG_AVAILABLE or self.watching:
            return

        try:
            from watchdog.events import FileSystemEventHandler

            class IndexUpdateHandler(FileSystemEventHandler):
                def __init__(self, indexer):
                    self.indexer = indexer

                def on_modified(self, event):
                    if not event.is_directory:
                        self.indexer._handle_file_change(Path(event.src_path))

                def on_created(self, event):
                    if not event.is_directory:
                        self.indexer._handle_file_change(Path(event.src_path))

                def on_deleted(self, event):
                    if not event.is_directory:
                        self.indexer._handle_file_deletion(Path(event.src_path))

            self.observer = Observer()
            self.observer.schedule(IndexUpdateHandler(self), str(self.root_path), recursive=True)
            self.observer.start()
            self.watching = True

        except Exception as e:
            logger.error(f"Error starting file watcher: {e}")

    def stop_watching(self):
        """Stop watching for file changes."""
        if self.observer and self.watching:
            self.observer.stop()
            self.observer.join()
            self.watching = False

    def _handle_file_change(self, file_path: Path):
        """Handle file change event."""
        try:
            if self._should_index_file(file_path):
                self._index_file(file_path, force_rebuild=True)
        except Exception as e:
            logger.debug(f"Error handling file change {file_path}: {e}")

    def _handle_file_deletion(self, file_path: Path):
        """Handle file deletion event."""
        try:
            with self.cache_lock:
                if str(file_path) in self.index_cache:
                    del self.index_cache[str(file_path)]
        except Exception as e:
            logger.debug(f"Error handling file deletion {file_path}: {e}")

    def _should_index_file(self, file_path: Path) -> bool:
        """Check if a file should be indexed."""
        # Skip excluded directories
        if any(excluded_dir in file_path.parts for excluded_dir in self.excluded_dirs):
            return False

        # Skip excluded extensions
        if file_path.suffix.lower() in self.excluded_extensions:
            return False

        # Skip very large files
        try:
            if file_path.stat().st_size > 10 * 1024 * 1024:
                return False
        except OSError:
            return False

        return True

    def format_search_results(self, results: List[SearchResult]) -> str:
        """Format search results for display."""
        if not results:
            return "No results found."

        output = []
        output.append(f"Found {len(results)} results:")
        output.append("=" * 60)

        for i, result in enumerate(results, 1):
            output.append(f"\n{i}. {result.file_path}")
            output.append(f"   Relevance: {result.relevance_score:.3f} | Type: {result.match_type}")

            if result.matched_content:
                output.append(f"   Matches: {', '.join(result.matched_content[:5])}")
                if len(result.matched_content) > 5:
                    output.append(f"   ... and {len(result.matched_content) - 5} more")

            if result.line_numbers:
                lines_str = ', '.join(map(str, result.line_numbers[:5]))
                output.append(f"   Lines: {lines_str}")
                if len(result.line_numbers) > 5:
                    output.append(f"   ... and {len(result.line_numbers) - 5} more")

        return "\n".join(output)
