#!/usr/bin/env python3

"""
Comprehensive Examples for Advanced AI Coding Features
This file demonstrates all the new advanced features with practical examples.
"""

# =============================================================================
# 1. ADVANCED PATTERN SEARCH EXAMPLES
# =============================================================================

def example_advanced_pattern_search():
    """Examples of advanced pattern search capabilities."""
    
    print("🔍 ADVANCED PATTERN SEARCH EXAMPLES")
    print("=" * 50)
    
    # Example 1: Semantic Search for Concepts
    print("\n1. Semantic Search for Authentication:")
    print("""
    advanced_pattern_search(
        query="authentication jwt token validation middleware",
        search_mode="semantic",
        file_types=[".py", ".js", ".ts"],
        max_results=20,
        context_lines=5,
        include_tests=False
    )
    
    # This will find:
    # - Authentication-related functions and classes
    # - JWT token handling code
    # - Validation middleware implementations
    # - Security-related patterns
    """)
    
    # Example 2: Complex Regex Pattern Search
    print("\n2. Complex Regex Pattern Search:")
    print("""
    advanced_pattern_search(
        query="gemini.*model|GEMINI.*MODEL|model.*gemini|ModelGemini",
        search_mode="regex",
        file_types=[".py"],
        max_results=30,
        context_lines=3
    )
    
    # This will find:
    # - gemini_model, gemini.model, GeminiModel
    # - GEMINI_MODEL, GEMINI.MODEL
    # - model_gemini, ModelGemini
    # - Any variations of gemini and model combinations
    """)
    
    # Example 3: Natural Language Search
    print("\n3. Natural Language Search:")
    print("""
    advanced_pattern_search(
        query="find all database connection pooling implementations",
        search_mode="natural_language",
        max_results=15,
        similarity_threshold=0.7
    )
    
    # This will find:
    # - Database connection pool classes
    # - Connection management functions
    # - Pool configuration code
    # - Related database utilities
    """)
    
    # Example 4: Fuzzy Search for Approximate Matches
    print("\n4. Fuzzy Search for Approximate Matches:")
    print("""
    advanced_pattern_search(
        query="autentication",  # Misspelled intentionally
        search_mode="fuzzy",
        similarity_threshold=0.8,
        max_results=10
    )
    
    # This will find:
    # - "authentication" (correcting the typo)
    # - "authenticate", "authenticator"
    # - Similar sounding/looking terms
    """)
    
    # Example 5: Hybrid Search (Best of All Modes)
    print("\n5. Hybrid Search (Recommended):")
    print("""
    advanced_pattern_search(
        query="api rate limiting implementation",
        search_mode="hybrid",
        file_types=[".py", ".js", ".go"],
        max_results=25,
        context_lines=4,
        include_tests=True
    )
    
    # This combines:
    # - Semantic understanding of "rate limiting"
    # - Regex patterns for API-related code
    # - Fuzzy matching for variations
    # - Natural language processing
    """)

# =============================================================================
# 2. SMART CODE REPLACEMENT EXAMPLES
# =============================================================================

def example_smart_code_replacement():
    """Examples of smart code replacement capabilities."""
    
    print("\n🔧 SMART CODE REPLACEMENT EXAMPLES")
    print("=" * 50)
    
    # Example 1: Simple String Replacement with Preview
    print("\n1. Simple String Replacement with Preview:")
    print("""
    smart_replace_code(
        search_pattern="old_api_endpoint",
        replacement="new_api_endpoint",
        preview_only=True,
        backup_files=True
    )
    
    # This will:
    # - Show preview of all changes
    # - Create backups before modification
    # - Display affected files and line numbers
    """)
    
    # Example 2: Function Signature Refactoring
    print("\n2. Function Signature Refactoring:")
    print("""
    smart_replace_code(
        search_pattern="authenticate_user",
        replacement="authenticate_user_with_mfa",
        replacement_mode="function_signature",
        file_paths=["auth/", "api/"],
        preview_only=False,
        git_commit=True,
        backup_files=True
    )
    
    # This will:
    # - Update function definitions
    # - Update function calls
    # - Handle imports and references
    # - Commit changes to Git automatically
    """)
    
    # Example 3: Variable Renaming with Scope Awareness
    print("\n3. Variable Renaming with Scope Awareness:")
    print("""
    smart_replace_code(
        search_pattern="db_connection",
        replacement="database_pool",
        replacement_mode="variable_rename",
        preview_only=True
    )
    
    # This will:
    # - Rename variables within their scope
    # - Avoid conflicts with other variables
    # - Update all references correctly
    """)
    
    # Example 4: Regex-based Complex Replacement
    print("\n4. Regex-based Complex Replacement:")
    print("""
    smart_replace_code(
        search_pattern=r"print\s*\(([^)]+)\)",
        replacement=r"logger.info(\1)",
        replacement_mode="regex",
        file_paths=["src/"],
        preview_only=False,
        backup_files=True,
        git_commit=True
    )
    
    # This will:
    # - Replace all print statements with logger.info
    # - Preserve the original arguments
    # - Apply only to files in src/ directory
    """)
    
    # Example 5: Class Renaming
    print("\n5. Class Renaming:")
    print("""
    smart_replace_code(
        search_pattern="UserManager",
        replacement="UserService",
        replacement_mode="class_rename",
        preview_only=True,
        backup_files=True
    )
    
    # This will:
    # - Update class definitions
    # - Update class instantiations
    # - Update imports and references
    # - Handle inheritance relationships
    """)

# =============================================================================
# 3. SMART FILE SEARCH EXAMPLES
# =============================================================================

def example_smart_file_search():
    """Examples of smart file search capabilities."""
    
    print("\n🔎 SMART FILE SEARCH (grep++) EXAMPLES")
    print("=" * 50)
    
    # Example 1: Function Search
    print("\n1. Function Search:")
    print("""
    smart_file_search(
        query="authenticate",
        search_type="function",
        include_context=True,
        context_lines=5,
        max_results=20
    )
    
    # This will find:
    # - Function definitions containing "authenticate"
    # - Method definitions in classes
    # - Lambda functions and arrow functions
    # - With surrounding context for understanding
    """)
    
    # Example 2: Class Search
    print("\n2. Class Search:")
    print("""
    smart_file_search(
        query="Model",
        search_type="class",
        file_patterns=["*.py", "*.js"],
        include_context=True,
        case_sensitive=False
    )
    
    # This will find:
    # - Class definitions with "Model" in name
    # - Abstract classes and interfaces
    # - Class inheritance relationships
    """)
    
    # Example 3: Import Search
    print("\n3. Import Search:")
    print("""
    smart_file_search(
        query="flask",
        search_type="import",
        include_context=True,
        max_results=30
    )
    
    # This will find:
    # - import flask
    # - from flask import ...
    # - require('flask') in JavaScript
    # - All import variations
    """)
    
    # Example 4: Semantic Search
    print("\n4. Semantic Search:")
    print("""
    smart_file_search(
        query="database connection error handling",
        search_type="semantic",
        include_context=True,
        context_lines=4,
        max_results=15
    )
    
    # This will find:
    # - Error handling code for databases
    # - Connection retry logic
    # - Exception handling patterns
    # - Related error management code
    """)
    
    # Example 5: Comment Search
    print("\n5. Comment Search:")
    print("""
    smart_file_search(
        query="TODO",
        search_type="comment",
        include_context=True,
        file_patterns=["*.py", "*.js", "*.java"],
        max_results=50
    )
    
    # This will find:
    # - TODO comments
    # - FIXME comments
    # - NOTE comments
    # - Any comment containing the query
    """)
    
    # Example 6: Fuzzy Search
    print("\n6. Fuzzy Search:")
    print("""
    smart_file_search(
        query="autentication",  # Misspelled
        search_type="fuzzy",
        include_context=True,
        max_results=20
    )
    
    # This will find:
    # - "authentication" (corrected)
    # - "authenticate", "authenticator"
    # - Similar terms with typo tolerance
    """)

# =============================================================================
# 4. AUTO PLANNER EXAMPLES
# =============================================================================

def example_auto_planner():
    """Examples of auto planner capabilities."""
    
    print("\n🤖 AUTO PLANNER EXAMPLES")
    print("=" * 50)
    
    # Example 1: Create a Comprehensive Plan
    print("\n1. Create a Comprehensive Development Plan:")
    print("""
    create_execution_plan(
        user_request="Add user authentication with JWT tokens, rate limiting, and comprehensive testing",
        strategy="waterfall",
        auto_execute=False,
        auto_commit=True,
        context={
            "project_type": "web_api",
            "framework": "flask",
            "database": "postgresql"
        }
    )
    
    # This will create:
    # - Task 1: Analyze existing authentication system
    # - Task 2: Design JWT token implementation
    # - Task 3: Implement authentication middleware
    # - Task 4: Add rate limiting functionality
    # - Task 5: Create comprehensive tests
    # - Task 6: Update documentation
    # - Task 7: Git commit and deployment
    """)
    
    # Example 2: Parallel Execution Plan
    print("\n2. Parallel Execution Plan:")
    print("""
    create_execution_plan(
        user_request="Refactor codebase for better performance and add monitoring",
        strategy="parallel",
        auto_execute=True,
        auto_commit=True
    )
    
    # This will create parallel tasks:
    # - Code analysis and performance profiling
    # - Database query optimization
    # - Caching implementation
    # - Monitoring and logging setup
    # - Documentation updates
    """)
    
    # Example 3: Execute an Existing Plan
    print("\n3. Execute an Existing Plan:")
    print("""
    execute_plan(
        plan_id="plan_1234567890",
        auto_commit=True
    )
    
    # This will:
    # - Execute all tasks in the plan
    # - Show real-time progress
    # - Handle errors and dependencies
    # - Commit successful changes
    """)
    
    # Example 4: List All Plans
    print("\n4. List All Execution Plans:")
    print("""
    list_execution_plans()
    
    # This will show:
    # - All created plans with status
    # - Success rates and completion times
    # - Task counts and descriptions
    # - Creation dates and strategies used
    """)
    
    # Example 5: Agile Development Plan
    print("\n5. Agile Development Plan:")
    print("""
    create_execution_plan(
        user_request="Implement user dashboard with real-time updates",
        strategy="agile",
        auto_execute=False,
        context={
            "sprint_length": "2_weeks",
            "team_size": "small",
            "priority": "high"
        }
    )
    
    # This creates an agile plan with:
    # - Sprint planning and backlog creation
    # - Iterative development cycles
    # - Regular testing and feedback loops
    # - Continuous integration setup
    """)

# =============================================================================
# 5. INPUT FIXER EXAMPLES
# =============================================================================

def example_input_fixer():
    """Examples of input fixer capabilities."""
    
    print("\n🔧 INPUT FIXER EXAMPLES")
    print("=" * 50)
    
    # Example 1: Fix Python Syntax Errors
    print("\n1. Fix Python Syntax Errors:")
    print("""
    fix_code_input(
        code='''
        def broken_function(
            print "Hello World"
            if x = 5
                return True
        ''',
        language="python",
        fix_types=["syntax_error", "indentation", "formatting"]
    )
    
    # This will fix:
    # - Missing closing parenthesis
    # - print statement to print()
    # - Assignment vs comparison (= vs ==)
    # - Indentation issues
    # - Code formatting
    """)
    
    # Example 2: Fix JavaScript Issues
    print("\n2. Fix JavaScript Issues:")
    print("""
    fix_code_input(
        code='''
        function greetUser(name {
            console.log("Hello " + name)
            return true
        }
        ''',
        language="javascript",
        fix_types=["brackets", "semicolons", "formatting"]
    )
    
    # This will fix:
    # - Missing opening parenthesis
    # - Missing semicolons
    # - Code formatting and indentation
    """)
    
    # Example 3: Fix JSON Format
    print("\n3. Fix JSON Format:")
    print("""
    fix_code_input(
        code='''
        {
            "name": "John",
            "age": 30,
            "city": "New York"
        }
        ''',
        language="json",
        fix_types=["formatting", "quotes"]
    )
    
    # This will fix:
    # - Proper JSON formatting
    # - Quote consistency
    # - Trailing commas if any
    """)
    
    # Example 4: Auto-detect Language and Fix All Issues
    print("\n4. Auto-detect Language and Fix All Issues:")
    print("""
    fix_code_input(
        code=malformed_code_string
        # language auto-detected
        # fix_types defaults to all available fixes
    )
    
    # This will:
    # - Automatically detect the programming language
    # - Apply all relevant fixes
    # - Provide confidence score
    # - Show before/after comparison
    """)
    
    # Example 5: Fix Encoding Issues
    print("\n5. Fix Encoding Issues:")
    print("""
    fix_code_input(
        code=code_with_encoding_issues,
        fix_types=["encoding", "quotes", "whitespace"]
    )
    
    # This will fix:
    # - Unicode encoding problems
    # - Smart quotes to regular quotes
    # - Whitespace and line ending issues
    # - BOM (Byte Order Mark) removal
    """)

# =============================================================================
# 6. FILE INDEXING EXAMPLES
# =============================================================================

def example_file_indexing():
    """Examples of file indexing capabilities."""
    
    print("\n📚 FILE INDEXING EXAMPLES")
    print("=" * 50)
    
    # Example 1: Build Complete Index
    print("\n1. Build Complete Codebase Index:")
    print("""
    build_file_index(force_rebuild=False)
    
    # This will:
    # - Index all source code files
    # - Extract functions, classes, imports
    # - Build semantic understanding
    # - Create searchable database
    # - Enable real-time file monitoring
    """)
    
    # Example 2: Semantic Search in Index
    print("\n2. Semantic Search in Index:")
    print("""
    search_file_index(
        query="authentication middleware implementation",
        search_type="semantic",
        max_results=25,
        file_types=["source_code"]
    )
    
    # This will find:
    # - Files with authentication-related code
    # - Middleware implementations
    # - Related security patterns
    # - Ranked by semantic relevance
    """)
    
    # Example 3: Structural Search
    print("\n3. Structural Search:")
    print("""
    search_file_index(
        query="UserManager",
        search_type="structural",
        max_results=20
    )
    
    # This will find:
    # - Classes named UserManager
    # - Functions with UserManager in name
    # - Import statements for UserManager
    # - Variable assignments
    """)
    
    # Example 4: Full-text Search
    print("\n4. Full-text Search:")
    print("""
    search_file_index(
        query="database connection pool",
        search_type="full_text",
        max_results=30,
        file_types=["source_code", "documentation"]
    )
    
    # This will find:
    # - All files containing these terms
    # - Comments and documentation
    # - String literals and variable names
    # - Configuration files
    """)
    
    # Example 5: Get Index Statistics
    print("\n5. Get Index Statistics:")
    print("""
    get_index_stats()
    
    # This will show:
    # - Total files indexed
    # - File types breakdown
    # - Programming languages detected
    # - Index size and performance metrics
    # - Last update timestamp
    """)

# =============================================================================
# 7. COMBINED WORKFLOW EXAMPLES
# =============================================================================

def example_combined_workflows():
    """Examples of combining multiple advanced features."""
    
    print("\n🚀 COMBINED WORKFLOW EXAMPLES")
    print("=" * 50)
    
    # Example 1: Complete Code Refactoring Workflow
    print("\n1. Complete Code Refactoring Workflow:")
    print("""
    # Step 1: Build index for codebase understanding
    build_file_index(force_rebuild=True)
    
    # Step 2: Find all authentication-related code
    auth_files = advanced_pattern_search(
        query="authentication login logout session",
        search_mode="semantic",
        max_results=50
    )
    
    # Step 3: Create refactoring plan
    plan = create_execution_plan(
        user_request="Refactor authentication system to use JWT tokens",
        strategy="waterfall",
        context={"files_to_modify": auth_files}
    )
    
    # Step 4: Preview changes
    smart_replace_code(
        search_pattern="session_auth",
        replacement="jwt_auth",
        replacement_mode="function_signature",
        preview_only=True
    )
    
    # Step 5: Execute the plan
    execute_plan(plan_id=plan.id, auto_commit=True)
    """)
    
    # Example 2: Bug Investigation and Fix Workflow
    print("\n2. Bug Investigation and Fix Workflow:")
    print("""
    # Step 1: Search for error patterns
    error_locations = smart_file_search(
        query="NullPointerException|AttributeError|TypeError",
        search_type="regex",
        include_context=True,
        context_lines=10
    )
    
    # Step 2: Analyze code quality
    for location in error_locations:
        fix_code_input(
            code=location.content,
            fix_types=["syntax_error", "formatting"]
        )
    
    # Step 3: Create comprehensive fix plan
    create_execution_plan(
        user_request="Fix all null pointer exceptions and add proper error handling",
        strategy="parallel",
        auto_execute=True
    )
    """)
    
    # Example 3: New Feature Development Workflow
    print("\n3. New Feature Development Workflow:")
    print("""
    # Step 1: Research existing implementations
    existing_patterns = search_file_index(
        query="rate limiting throttling",
        search_type="semantic",
        file_types=["source_code"]
    )
    
    # Step 2: Create development plan
    plan = create_execution_plan(
        user_request="Implement API rate limiting with Redis backend",
        strategy="agile",
        context={"existing_patterns": existing_patterns}
    )
    
    # Step 3: Execute with monitoring
    execute_plan(plan_id=plan.id, auto_commit=True)
    
    # Step 4: Update related code
    smart_replace_code(
        search_pattern="@app.route",
        replacement="@rate_limited\\<EMAIL>",
        replacement_mode="regex",
        preview_only=False,
        git_commit=True
    )
    """)

if __name__ == "__main__":
    print("🎯 ADVANCED AI CODING FEATURES - COMPREHENSIVE EXAMPLES")
    print("=" * 70)
    print("This file contains detailed examples of all advanced features.")
    print("Copy and paste these examples into CODY to see them in action!")
    print("=" * 70)
    
    example_advanced_pattern_search()
    example_smart_code_replacement()
    example_smart_file_search()
    example_auto_planner()
    example_input_fixer()
    example_file_indexing()
    example_combined_workflows()
    
    print("\n🎉 All examples completed!")
    print("Use these patterns to leverage CODY's advanced AI coding capabilities!")
