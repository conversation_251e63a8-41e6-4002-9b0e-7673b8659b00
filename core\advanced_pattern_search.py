#!/usr/bin/env python3

"""
Advanced Pattern Search Module for CODY Agent
Provides sophisticated search capabilities with semantic understanding, fuzzy matching,
and natural language queries across entire codebase.
"""

import os
import re
import ast
import json
import time
import asyncio
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed

# Third-party imports with fallbacks
try:
    from thefuzz import fuzz, process as fuzzy_process
    FUZZY_AVAILABLE = True
except ImportError:
    FUZZY_AVAILABLE = False

try:
    import tree_sitter
    from tree_sitter import Language, Parser
    TREE_SITTER_AVAILABLE = True
except ImportError:
    TREE_SITTER_AVAILABLE = False

logger = logging.getLogger('CODY.AdvancedPatternSearch')

class SearchMode(Enum):
    """Search modes for pattern matching."""
    SEMANTIC = "semantic"
    FUZZY = "fuzzy"
    REGEX = "regex"
    NATURAL_LANGUAGE = "natural_language"
    HYBRID = "hybrid"

class SearchResultType(Enum):
    """Types of search results."""
    FUNCTION = "function"
    CLASS = "class"
    VARIABLE = "variable"
    IMPORT = "import"
    COMMENT = "comment"
    STRING_LITERAL = "string_literal"
    GENERIC_MATCH = "generic_match"

@dataclass
class AdvancedSearchResult:
    """Enhanced search result with rich metadata."""
    file_path: str
    line_number: int
    line_content: str
    match_text: str
    result_type: SearchResultType
    confidence_score: float
    context_before: List[str] = field(default_factory=list)
    context_after: List[str] = field(default_factory=list)
    function_name: Optional[str] = None
    class_name: Optional[str] = None
    semantic_tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class SearchStats:
    """Statistics for search operations."""
    files_searched: int = 0
    total_matches: int = 0
    search_time: float = 0.0
    excluded_files: int = 0
    error_files: int = 0

class AdvancedPatternSearchEngine:
    """
    Advanced pattern search engine with multiple search modes and semantic understanding.
    """
    
    def __init__(self, root_path: str = "."):
        self.root_path = Path(root_path)
        self.excluded_extensions = {
            '.pyc', '.pyo', '.pyd', '.so', '.dll', '.dylib', '.exe', '.bin',
            '.png', '.jpg', '.jpeg', '.gif', '.ico', '.svg', '.webp',
            '.mp4', '.webm', '.mov', '.mp3', '.wav', '.ogg',
            '.zip', '.tar', '.gz', '.7z', '.rar', '.pdf', '.doc', '.docx'
        }
        self.excluded_dirs = {
            '__pycache__', '.git', '.svn', '.hg', 'node_modules', '.venv', 'venv',
            '.pytest_cache', '.mypy_cache', 'dist', 'build', '.cache'
        }
        
        # Language-specific patterns for semantic search
        self.language_patterns = {
            'python': {
                'function': r'def\s+(\w+)\s*\(',
                'class': r'class\s+(\w+)\s*[\(:]',
                'import': r'(?:from\s+\w+\s+)?import\s+([^\n]+)',
                'variable': r'(\w+)\s*=\s*[^=]',
                'decorator': r'@(\w+)',
            },
            'javascript': {
                'function': r'(?:function\s+(\w+)|(\w+)\s*=\s*(?:function|\([^)]*\)\s*=>))',
                'class': r'class\s+(\w+)',
                'import': r'import\s+.*?from\s+[\'"]([^\'"]+)[\'"]',
                'variable': r'(?:let|const|var)\s+(\w+)',
            },
            'java': {
                'function': r'(?:public|private|protected)?\s*(?:static)?\s*\w+\s+(\w+)\s*\(',
                'class': r'(?:public|private)?\s*class\s+(\w+)',
                'import': r'import\s+([^;]+);',
            }
        }
        
        # Natural language to code pattern mappings
        self.nl_patterns = {
            'model': ['model', 'Model', 'MODEL'],
            'gemini': ['gemini', 'Gemini', 'GEMINI'],
            'function': ['def ', 'function ', 'func '],
            'class': ['class ', 'Class'],
            'error': ['error', 'Error', 'exception', 'Exception'],
            'test': ['test_', 'Test', '_test'],
            'config': ['config', 'Config', 'configuration', 'settings'],
            'api': ['api', 'API', 'endpoint', 'route'],
            'database': ['db', 'database', 'Database', 'sql', 'query'],
        }

    def search(self, query: str, search_mode: SearchMode = SearchMode.HYBRID,
               file_types: Optional[List[str]] = None, context_lines: int = 3,
               max_results: int = 50, include_tests: bool = False,
               similarity_threshold: float = 0.8) -> Tuple[List[AdvancedSearchResult], SearchStats]:
        """
        Perform advanced pattern search across the codebase.
        
        Args:
            query: Search query (natural language, regex, or specific pattern)
            search_mode: Search mode to use
            file_types: File extensions to search
            context_lines: Number of context lines around matches
            max_results: Maximum number of results
            include_tests: Whether to include test files
            similarity_threshold: Similarity threshold for fuzzy matching
            
        Returns:
            Tuple of (search results, search statistics)
        """
        start_time = time.time()
        stats = SearchStats()
        
        # Get files to search
        files_to_search = self._get_files_to_search(file_types, include_tests)
        stats.files_searched = len(files_to_search)
        
        # Perform search based on mode
        if search_mode == SearchMode.SEMANTIC:
            results = self._semantic_search(query, files_to_search, context_lines)
        elif search_mode == SearchMode.FUZZY:
            results = self._fuzzy_search(query, files_to_search, context_lines, similarity_threshold)
        elif search_mode == SearchMode.REGEX:
            results = self._regex_search(query, files_to_search, context_lines)
        elif search_mode == SearchMode.NATURAL_LANGUAGE:
            results = self._natural_language_search(query, files_to_search, context_lines)
        else:  # HYBRID
            results = self._hybrid_search(query, files_to_search, context_lines, similarity_threshold)
        
        # Sort by confidence and limit results
        results.sort(key=lambda x: x.confidence_score, reverse=True)
        results = results[:max_results]
        
        stats.total_matches = len(results)
        stats.search_time = time.time() - start_time
        
        return results, stats

    def _get_files_to_search(self, file_types: Optional[List[str]], include_tests: bool) -> List[Path]:
        """Get list of files to search based on criteria."""
        files = []
        
        for file_path in self.root_path.rglob('*'):
            if not file_path.is_file():
                continue
                
            # Skip excluded directories
            if any(excluded_dir in file_path.parts for excluded_dir in self.excluded_dirs):
                continue
                
            # Skip excluded extensions
            if file_path.suffix.lower() in self.excluded_extensions:
                continue
                
            # Filter by file types if specified
            if file_types and file_path.suffix not in file_types:
                continue
                
            # Skip test files if not included
            if not include_tests and ('test' in file_path.name.lower() or 'spec' in file_path.name.lower()):
                continue
                
            files.append(file_path)
        
        return files

    def _semantic_search(self, query: str, files: List[Path], context_lines: int) -> List[AdvancedSearchResult]:
        """Perform semantic search using code structure understanding."""
        results = []
        
        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    lines = content.splitlines()
                
                # Detect language
                language = self._detect_language(file_path)
                if language not in self.language_patterns:
                    continue
                
                # Search for semantic patterns
                patterns = self.language_patterns[language]
                for pattern_type, pattern in patterns.items():
                    matches = re.finditer(pattern, content, re.MULTILINE | re.IGNORECASE)
                    
                    for match in matches:
                        if query.lower() in match.group().lower():
                            line_num = content[:match.start()].count('\n') + 1
                            line_content = lines[line_num - 1] if line_num <= len(lines) else ""
                            
                            result = AdvancedSearchResult(
                                file_path=str(file_path),
                                line_number=line_num,
                                line_content=line_content.strip(),
                                match_text=match.group(),
                                result_type=SearchResultType(pattern_type) if pattern_type in [e.value for e in SearchResultType] else SearchResultType.GENERIC_MATCH,
                                confidence_score=0.9,  # High confidence for semantic matches
                                context_before=self._get_context_lines(lines, line_num - 1, context_lines, before=True),
                                context_after=self._get_context_lines(lines, line_num - 1, context_lines, before=False),
                                semantic_tags=[pattern_type, language]
                            )
                            results.append(result)
                            
            except Exception as e:
                logger.debug(f"Error searching file {file_path}: {e}")
                continue
        
        return results

    def _fuzzy_search(self, query: str, files: List[Path], context_lines: int,
                     similarity_threshold: float) -> List[AdvancedSearchResult]:
        """Perform fuzzy search using string similarity."""
        if not FUZZY_AVAILABLE:
            logger.warning("Fuzzy search not available. Install thefuzz package.")
            return []

        results = []

        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()

                for i, line in enumerate(lines):
                    # Calculate fuzzy similarity
                    similarity = fuzz.partial_ratio(query.lower(), line.lower()) / 100.0

                    if similarity >= similarity_threshold:
                        result = AdvancedSearchResult(
                            file_path=str(file_path),
                            line_number=i + 1,
                            line_content=line.strip(),
                            match_text=line.strip(),
                            result_type=SearchResultType.GENERIC_MATCH,
                            confidence_score=similarity,
                            context_before=self._get_context_lines(lines, i, context_lines, before=True),
                            context_after=self._get_context_lines(lines, i, context_lines, before=False),
                            semantic_tags=['fuzzy_match']
                        )
                        results.append(result)

            except Exception as e:
                logger.debug(f"Error in fuzzy search for file {file_path}: {e}")
                continue

        return results

    def _regex_search(self, query: str, files: List[Path], context_lines: int) -> List[AdvancedSearchResult]:
        """Perform regex-based search."""
        results = []

        try:
            pattern = re.compile(query, re.MULTILINE | re.IGNORECASE)
        except re.error as e:
            logger.error(f"Invalid regex pattern: {query}, error: {e}")
            return []

        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    lines = content.splitlines()

                for match in pattern.finditer(content):
                    line_num = content[:match.start()].count('\n') + 1
                    line_content = lines[line_num - 1] if line_num <= len(lines) else ""

                    result = AdvancedSearchResult(
                        file_path=str(file_path),
                        line_number=line_num,
                        line_content=line_content.strip(),
                        match_text=match.group(),
                        result_type=SearchResultType.GENERIC_MATCH,
                        confidence_score=0.95,  # High confidence for exact regex matches
                        context_before=self._get_context_lines(lines, line_num - 1, context_lines, before=True),
                        context_after=self._get_context_lines(lines, line_num - 1, context_lines, before=False),
                        semantic_tags=['regex_match']
                    )
                    results.append(result)

            except Exception as e:
                logger.debug(f"Error in regex search for file {file_path}: {e}")
                continue

        return results

    def _natural_language_search(self, query: str, files: List[Path], context_lines: int) -> List[AdvancedSearchResult]:
        """Perform natural language search by mapping to code patterns."""
        results = []

        # Extract keywords from natural language query
        keywords = self._extract_keywords_from_nl(query)

        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    lines = content.splitlines()

                # Search for each keyword
                for keyword in keywords:
                    for i, line in enumerate(lines):
                        if keyword.lower() in line.lower():
                            confidence = self._calculate_nl_confidence(query, line, keyword)

                            if confidence > 0.3:  # Minimum threshold
                                result = AdvancedSearchResult(
                                    file_path=str(file_path),
                                    line_number=i + 1,
                                    line_content=line.strip(),
                                    match_text=keyword,
                                    result_type=SearchResultType.GENERIC_MATCH,
                                    confidence_score=confidence,
                                    context_before=self._get_context_lines(lines, i, context_lines, before=True),
                                    context_after=self._get_context_lines(lines, i, context_lines, before=False),
                                    semantic_tags=['natural_language', keyword]
                                )
                                results.append(result)

            except Exception as e:
                logger.debug(f"Error in natural language search for file {file_path}: {e}")
                continue

        return results

    def _hybrid_search(self, query: str, files: List[Path], context_lines: int,
                      similarity_threshold: float) -> List[AdvancedSearchResult]:
        """Perform hybrid search combining multiple search modes."""
        all_results = []

        # Try semantic search first
        semantic_results = self._semantic_search(query, files, context_lines)
        for result in semantic_results:
            result.semantic_tags.append('hybrid_semantic')
        all_results.extend(semantic_results)

        # Try regex search if query looks like regex
        if self._is_regex_pattern(query):
            regex_results = self._regex_search(query, files, context_lines)
            for result in regex_results:
                result.semantic_tags.append('hybrid_regex')
            all_results.extend(regex_results)

        # Try fuzzy search for partial matches
        if FUZZY_AVAILABLE:
            fuzzy_results = self._fuzzy_search(query, files, context_lines, similarity_threshold * 0.7)
            for result in fuzzy_results:
                result.semantic_tags.append('hybrid_fuzzy')
                result.confidence_score *= 0.8  # Slightly lower confidence for fuzzy in hybrid
            all_results.extend(fuzzy_results)

        # Try natural language search
        nl_results = self._natural_language_search(query, files, context_lines)
        for result in nl_results:
            result.semantic_tags.append('hybrid_nl')
        all_results.extend(nl_results)

        # Remove duplicates based on file_path and line_number
        unique_results = []
        seen = set()

        for result in all_results:
            key = (result.file_path, result.line_number)
            if key not in seen:
                seen.add(key)
                unique_results.append(result)

        return unique_results

    def _detect_language(self, file_path: Path) -> str:
        """Detect programming language from file extension."""
        extension = file_path.suffix.lower()
        language_map = {
            '.py': 'python',
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.ts': 'javascript',
            '.tsx': 'javascript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'cpp',
            '.h': 'cpp',
            '.hpp': 'cpp',
            '.go': 'go',
            '.rs': 'rust',
            '.php': 'php',
            '.rb': 'ruby',
            '.cs': 'csharp',
            '.swift': 'swift',
            '.kt': 'kotlin'
        }
        return language_map.get(extension, 'unknown')

    def _get_context_lines(self, lines: List[str], line_index: int, context_lines: int,
                          before: bool = True) -> List[str]:
        """Get context lines before or after a specific line."""
        if before:
            start = max(0, line_index - context_lines)
            end = line_index
            return [line.strip() for line in lines[start:end]]
        else:
            start = line_index + 1
            end = min(len(lines), line_index + 1 + context_lines)
            return [line.strip() for line in lines[start:end]]

    def _extract_keywords_from_nl(self, query: str) -> List[str]:
        """Extract keywords from natural language query."""
        keywords = []

        # Check predefined patterns
        for concept, patterns in self.nl_patterns.items():
            if any(pattern.lower() in query.lower() for pattern in patterns):
                keywords.extend(patterns)

        # Extract individual words as potential keywords
        words = re.findall(r'\b\w+\b', query.lower())
        keywords.extend(words)

        # Remove duplicates and common words
        common_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'}
        keywords = list(set(keyword for keyword in keywords if keyword.lower() not in common_words and len(keyword) > 2))

        return keywords

    def _calculate_nl_confidence(self, query: str, line: str, keyword: str) -> float:
        """Calculate confidence score for natural language matches."""
        base_confidence = 0.5

        # Boost confidence if keyword appears multiple times
        keyword_count = line.lower().count(keyword.lower())
        if keyword_count > 1:
            base_confidence += 0.1 * keyword_count

        # Boost confidence if line contains multiple query keywords
        query_words = set(re.findall(r'\b\w+\b', query.lower()))
        line_words = set(re.findall(r'\b\w+\b', line.lower()))
        overlap = len(query_words.intersection(line_words))
        if overlap > 1:
            base_confidence += 0.1 * overlap

        # Boost confidence for function/class definitions
        if any(pattern in line.lower() for pattern in ['def ', 'class ', 'function ']):
            base_confidence += 0.2

        return min(1.0, base_confidence)

    def _is_regex_pattern(self, query: str) -> bool:
        """Check if query looks like a regex pattern."""
        regex_indicators = [r'\*', r'\+', r'\?', r'\[', r'\]', r'\(', r'\)', r'\{', r'\}', r'\|', r'\^', r'\$', r'\.']
        return any(indicator in query for indicator in regex_indicators)

    def format_results(self, results: List[AdvancedSearchResult], stats: SearchStats) -> str:
        """Format search results for display."""
        if not results:
            return f"No matches found. Searched {stats.files_searched} files in {stats.search_time:.2f}s"

        output = []
        output.append(f"Found {len(results)} matches in {stats.files_searched} files ({stats.search_time:.2f}s)")
        output.append("=" * 80)

        for i, result in enumerate(results, 1):
            output.append(f"\n{i}. {result.file_path}:{result.line_number}")
            output.append(f"   Type: {result.result_type.value} | Confidence: {result.confidence_score:.2f}")
            output.append(f"   Tags: {', '.join(result.semantic_tags)}")

            # Show context
            if result.context_before:
                output.append("   Context before:")
                for line in result.context_before[-2:]:  # Show last 2 context lines
                    output.append(f"     {line}")

            output.append(f"   >>> {result.line_content}")

            if result.context_after:
                output.append("   Context after:")
                for line in result.context_after[:2]:  # Show first 2 context lines
                    output.append(f"     {line}")

            if i < len(results):
                output.append("-" * 40)

        return "\n".join(output)
