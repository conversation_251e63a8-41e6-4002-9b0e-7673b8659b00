#!/usr/bin/env python3

"""
Auto Planner Module for CODY Agent
Implements autonomous task decomposition and execution system with progress tracking and Git integration.
"""

import os
import json
import time
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
import logging
import asyncio
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger('CODY.AutoPlanner')

class TaskStatus(Enum):
    """Status of a task in the plan."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"

class TaskType(Enum):
    """Types of tasks that can be planned."""
    FILE_CREATION = "file_creation"
    FILE_MODIFICATION = "file_modification"
    CODE_ANALYSIS = "code_analysis"
    TESTING = "testing"
    DEBUGGING = "debugging"
    REFACTORING = "refactoring"
    DOCUMENTATION = "documentation"
    GIT_OPERATION = "git_operation"
    TERMINAL_COMMAND = "terminal_command"
    VALIDATION = "validation"

class PlanningStrategy(Enum):
    """Planning strategies for task decomposition."""
    WATERFALL = "waterfall"
    AGILE = "agile"
    INCREMENTAL = "incremental"
    PARALLEL = "parallel"

@dataclass
class Task:
    """Individual task in the execution plan."""
    id: str
    name: str
    description: str
    task_type: TaskType
    status: TaskStatus = TaskStatus.PENDING
    dependencies: List[str] = field(default_factory=list)
    estimated_duration: float = 0.0
    actual_duration: float = 0.0
    priority: int = 1  # 1 = highest, 5 = lowest
    parameters: Dict[str, Any] = field(default_factory=dict)
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    created_at: float = field(default_factory=time.time)
    started_at: Optional[float] = None
    completed_at: Optional[float] = None

@dataclass
class ExecutionPlan:
    """Complete execution plan with tasks and metadata."""
    id: str
    name: str
    description: str
    strategy: PlanningStrategy
    tasks: List[Task] = field(default_factory=list)
    status: TaskStatus = TaskStatus.PENDING
    created_at: float = field(default_factory=time.time)
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    total_estimated_duration: float = 0.0
    total_actual_duration: float = 0.0
    success_rate: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class PlanningResult:
    """Result of the planning process."""
    plan: ExecutionPlan
    success: bool
    message: str
    recommendations: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)

class AutoPlanner:
    """
    Autonomous task planner that decomposes complex requests into executable tasks.
    """
    
    def __init__(self, root_path: str = "."):
        self.root_path = Path(root_path)
        self.plans_dir = self.root_path / ".cody_plans"
        self.plans_dir.mkdir(exist_ok=True)
        
        # Task templates for common operations
        self.task_templates = {
            TaskType.FILE_CREATION: {
                "estimated_duration": 2.0,
                "parameters": {"file_path": "", "content": "", "template": ""}
            },
            TaskType.FILE_MODIFICATION: {
                "estimated_duration": 3.0,
                "parameters": {"file_path": "", "changes": [], "backup": True}
            },
            TaskType.CODE_ANALYSIS: {
                "estimated_duration": 5.0,
                "parameters": {"file_paths": [], "analysis_type": "structure"}
            },
            TaskType.TESTING: {
                "estimated_duration": 10.0,
                "parameters": {"test_files": [], "test_framework": "pytest"}
            },
            TaskType.GIT_OPERATION: {
                "estimated_duration": 1.0,
                "parameters": {"operation": "", "files": [], "message": ""}
            }
        }
        
        # Planning patterns for different types of requests
        self.planning_patterns = {
            "create_project": [
                TaskType.FILE_CREATION,  # Project structure
                TaskType.FILE_CREATION,  # Main files
                TaskType.DOCUMENTATION,  # README, docs
                TaskType.GIT_OPERATION,  # Initialize git
                TaskType.VALIDATION     # Verify project
            ],
            "add_feature": [
                TaskType.CODE_ANALYSIS,     # Analyze existing code
                TaskType.FILE_MODIFICATION, # Modify existing files
                TaskType.FILE_CREATION,     # Create new files if needed
                TaskType.TESTING,           # Add/update tests
                TaskType.DOCUMENTATION,     # Update documentation
                TaskType.GIT_OPERATION     # Commit changes
            ],
            "fix_bug": [
                TaskType.CODE_ANALYSIS,     # Analyze the bug
                TaskType.DEBUGGING,         # Debug the issue
                TaskType.FILE_MODIFICATION, # Fix the code
                TaskType.TESTING,           # Test the fix
                TaskType.GIT_OPERATION     # Commit fix
            ],
            "refactor_code": [
                TaskType.CODE_ANALYSIS,     # Analyze current code
                TaskType.REFACTORING,       # Refactor code
                TaskType.TESTING,           # Ensure tests pass
                TaskType.DOCUMENTATION,     # Update docs if needed
                TaskType.GIT_OPERATION     # Commit changes
            ]
        }

    def create_plan(self, user_request: str, strategy: PlanningStrategy = PlanningStrategy.WATERFALL,
                   context: Optional[Dict[str, Any]] = None) -> PlanningResult:
        """
        Create an execution plan from a user request.
        
        Args:
            user_request: Natural language description of what to do
            strategy: Planning strategy to use
            context: Additional context information
            
        Returns:
            PlanningResult with the generated plan
        """
        try:
            # Analyze the request to determine the type of work
            request_type = self._analyze_request_type(user_request)
            
            # Generate plan ID
            plan_id = f"plan_{int(time.time())}"
            
            # Create the execution plan
            plan = ExecutionPlan(
                id=plan_id,
                name=f"Auto Plan: {user_request[:50]}...",
                description=user_request,
                strategy=strategy,
                metadata={"request_type": request_type, "context": context or {}}
            )
            
            # Generate tasks based on request type and strategy
            tasks = self._generate_tasks(user_request, request_type, strategy, context)
            plan.tasks = tasks
            
            # Calculate dependencies and durations
            self._calculate_dependencies(plan)
            self._calculate_durations(plan)
            
            # Save the plan
            self._save_plan(plan)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(plan)
            
            return PlanningResult(
                plan=plan,
                success=True,
                message=f"Successfully created plan with {len(tasks)} tasks",
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"Error creating plan: {e}")
            return PlanningResult(
                plan=ExecutionPlan(id="error", name="Error", description="", strategy=strategy),
                success=False,
                message=f"Failed to create plan: {str(e)}"
            )

    def execute_plan(self, plan: ExecutionPlan, auto_commit: bool = False,
                    progress_callback: Optional[callable] = None) -> Dict[str, Any]:
        """
        Execute an execution plan.
        
        Args:
            plan: The execution plan to run
            auto_commit: Whether to automatically commit changes to Git
            progress_callback: Optional callback for progress updates
            
        Returns:
            Execution results
        """
        plan.status = TaskStatus.IN_PROGRESS
        plan.started_at = time.time()
        
        results = {
            "plan_id": plan.id,
            "success": True,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "skipped_tasks": 0,
            "task_results": [],
            "total_duration": 0.0,
            "error_messages": []
        }
        
        try:
            # Execute tasks based on strategy
            if plan.strategy == PlanningStrategy.WATERFALL:
                results = self._execute_waterfall(plan, results, progress_callback)
            elif plan.strategy == PlanningStrategy.PARALLEL:
                results = self._execute_parallel(plan, results, progress_callback)
            else:
                results = self._execute_sequential(plan, results, progress_callback)
            
            # Calculate final statistics
            plan.completed_at = time.time()
            plan.total_actual_duration = plan.completed_at - plan.started_at
            
            completed_tasks = [t for t in plan.tasks if t.status == TaskStatus.COMPLETED]
            plan.success_rate = len(completed_tasks) / len(plan.tasks) if plan.tasks else 0.0
            
            if plan.success_rate >= 0.8:
                plan.status = TaskStatus.COMPLETED
            else:
                plan.status = TaskStatus.FAILED
            
            results["success"] = plan.status == TaskStatus.COMPLETED
            results["total_duration"] = plan.total_actual_duration
            
            # Auto-commit if requested and successful
            if auto_commit and results["success"]:
                self._auto_commit_changes(plan)
            
            # Update saved plan
            self._save_plan(plan)
            
        except Exception as e:
            logger.error(f"Error executing plan: {e}")
            plan.status = TaskStatus.FAILED
            results["success"] = False
            results["error_messages"].append(str(e))
        
        return results

    def _analyze_request_type(self, request: str) -> str:
        """Analyze the user request to determine the type of work needed."""
        request_lower = request.lower()

        # Check for common patterns
        if any(word in request_lower for word in ['create', 'new', 'build', 'generate', 'make']):
            if any(word in request_lower for word in ['project', 'app', 'application']):
                return "create_project"
            else:
                return "create_feature"
        elif any(word in request_lower for word in ['fix', 'bug', 'error', 'issue', 'problem']):
            return "fix_bug"
        elif any(word in request_lower for word in ['refactor', 'improve', 'optimize', 'clean']):
            return "refactor_code"
        elif any(word in request_lower for word in ['add', 'implement', 'feature']):
            return "add_feature"
        elif any(word in request_lower for word in ['test', 'testing', 'unit test']):
            return "add_testing"
        elif any(word in request_lower for word in ['document', 'docs', 'documentation']):
            return "add_documentation"
        else:
            return "general_task"

    def _generate_tasks(self, request: str, request_type: str, strategy: PlanningStrategy,
                       context: Optional[Dict[str, Any]]) -> List[Task]:
        """Generate tasks based on the request type and strategy."""
        tasks = []

        # Get base task pattern
        task_pattern = self.planning_patterns.get(request_type, [TaskType.FILE_MODIFICATION])

        # Generate tasks based on pattern
        for i, task_type in enumerate(task_pattern):
            task_id = f"task_{i+1:03d}"
            task_name = self._generate_task_name(task_type, request)
            task_description = self._generate_task_description(task_type, request, context)

            # Get template parameters
            template = self.task_templates.get(task_type, {})

            task = Task(
                id=task_id,
                name=task_name,
                description=task_description,
                task_type=task_type,
                estimated_duration=template.get("estimated_duration", 5.0),
                parameters=template.get("parameters", {}).copy(),
                priority=i + 1  # Sequential priority
            )

            tasks.append(task)

        return tasks

    def _generate_task_name(self, task_type: TaskType, request: str) -> str:
        """Generate a descriptive name for a task."""
        task_names = {
            TaskType.FILE_CREATION: "Create Files",
            TaskType.FILE_MODIFICATION: "Modify Files",
            TaskType.CODE_ANALYSIS: "Analyze Code",
            TaskType.TESTING: "Run Tests",
            TaskType.DEBUGGING: "Debug Issues",
            TaskType.REFACTORING: "Refactor Code",
            TaskType.DOCUMENTATION: "Update Documentation",
            TaskType.GIT_OPERATION: "Git Operations",
            TaskType.TERMINAL_COMMAND: "Execute Commands",
            TaskType.VALIDATION: "Validate Results"
        }

        base_name = task_names.get(task_type, "Execute Task")

        # Add context from request
        if "test" in request.lower():
            base_name += " (Testing Focus)"
        elif "api" in request.lower():
            base_name += " (API Focus)"
        elif "ui" in request.lower():
            base_name += " (UI Focus)"

        return base_name

    def _generate_task_description(self, task_type: TaskType, request: str,
                                 context: Optional[Dict[str, Any]]) -> str:
        """Generate a detailed description for a task."""
        descriptions = {
            TaskType.FILE_CREATION: f"Create necessary files for: {request}",
            TaskType.FILE_MODIFICATION: f"Modify existing files to implement: {request}",
            TaskType.CODE_ANALYSIS: f"Analyze codebase to understand requirements for: {request}",
            TaskType.TESTING: f"Create and run tests to verify: {request}",
            TaskType.DEBUGGING: f"Debug and fix issues related to: {request}",
            TaskType.REFACTORING: f"Refactor code to improve: {request}",
            TaskType.DOCUMENTATION: f"Update documentation for: {request}",
            TaskType.GIT_OPERATION: f"Commit changes for: {request}",
            TaskType.TERMINAL_COMMAND: f"Execute commands needed for: {request}",
            TaskType.VALIDATION: f"Validate implementation of: {request}"
        }

        return descriptions.get(task_type, f"Execute task for: {request}")

    def _calculate_dependencies(self, plan: ExecutionPlan):
        """Calculate task dependencies based on strategy."""
        if plan.strategy == PlanningStrategy.WATERFALL:
            # Sequential dependencies
            for i in range(1, len(plan.tasks)):
                plan.tasks[i].dependencies.append(plan.tasks[i-1].id)
        elif plan.strategy == PlanningStrategy.PARALLEL:
            # Minimal dependencies - only critical path
            analysis_tasks = [t for t in plan.tasks if t.task_type == TaskType.CODE_ANALYSIS]
            modification_tasks = [t for t in plan.tasks if t.task_type in [TaskType.FILE_MODIFICATION, TaskType.FILE_CREATION]]
            test_tasks = [t for t in plan.tasks if t.task_type == TaskType.TESTING]

            # Modifications depend on analysis
            for mod_task in modification_tasks:
                for analysis_task in analysis_tasks:
                    mod_task.dependencies.append(analysis_task.id)

            # Tests depend on modifications
            for test_task in test_tasks:
                for mod_task in modification_tasks:
                    test_task.dependencies.append(mod_task.id)

    def _calculate_durations(self, plan: ExecutionPlan):
        """Calculate total estimated duration for the plan."""
        if plan.strategy == PlanningStrategy.PARALLEL:
            # Calculate critical path
            plan.total_estimated_duration = self._calculate_critical_path(plan.tasks)
        else:
            # Sequential execution
            plan.total_estimated_duration = sum(task.estimated_duration for task in plan.tasks)

    def _calculate_critical_path(self, tasks: List[Task]) -> float:
        """Calculate the critical path duration for parallel execution."""
        # Simplified critical path calculation
        # In a real implementation, this would use proper critical path method
        max_duration = 0.0

        # Group tasks by dependencies
        independent_tasks = [t for t in tasks if not t.dependencies]
        dependent_tasks = [t for t in tasks if t.dependencies]

        # Calculate parallel execution time
        if independent_tasks:
            max_duration += max(t.estimated_duration for t in independent_tasks)

        if dependent_tasks:
            max_duration += max(t.estimated_duration for t in dependent_tasks)

        return max_duration

    def _execute_waterfall(self, plan: ExecutionPlan, results: Dict[str, Any],
                          progress_callback: Optional[callable]) -> Dict[str, Any]:
        """Execute tasks in waterfall (sequential) order."""
        for task in plan.tasks:
            if progress_callback:
                progress_callback(f"Executing: {task.name}")

            task_result = self._execute_task(task)
            results["task_results"].append(task_result)

            if task.status == TaskStatus.COMPLETED:
                results["completed_tasks"] += 1
            elif task.status == TaskStatus.FAILED:
                results["failed_tasks"] += 1
                results["error_messages"].append(task.error_message or "Unknown error")
                # In waterfall, stop on failure
                break
            else:
                results["skipped_tasks"] += 1

        return results

    def _execute_parallel(self, plan: ExecutionPlan, results: Dict[str, Any],
                         progress_callback: Optional[callable]) -> Dict[str, Any]:
        """Execute tasks in parallel where possible."""
        # Group tasks by dependency level
        task_levels = self._group_tasks_by_dependency_level(plan.tasks)

        for level, tasks in task_levels.items():
            if progress_callback:
                progress_callback(f"Executing level {level}: {len(tasks)} tasks")

            # Execute tasks in this level in parallel
            with ThreadPoolExecutor(max_workers=min(4, len(tasks))) as executor:
                futures = {executor.submit(self._execute_task, task): task for task in tasks}

                for future in futures:
                    task = futures[future]
                    try:
                        task_result = future.result()
                        results["task_results"].append(task_result)

                        if task.status == TaskStatus.COMPLETED:
                            results["completed_tasks"] += 1
                        elif task.status == TaskStatus.FAILED:
                            results["failed_tasks"] += 1
                            results["error_messages"].append(task.error_message or "Unknown error")
                        else:
                            results["skipped_tasks"] += 1
                    except Exception as e:
                        task.status = TaskStatus.FAILED
                        task.error_message = str(e)
                        results["failed_tasks"] += 1
                        results["error_messages"].append(str(e))

        return results

    def _execute_sequential(self, plan: ExecutionPlan, results: Dict[str, Any],
                           progress_callback: Optional[callable]) -> Dict[str, Any]:
        """Execute tasks sequentially (default fallback)."""
        return self._execute_waterfall(plan, results, progress_callback)

    def _execute_task(self, task: Task) -> Dict[str, Any]:
        """Execute a single task."""
        task.status = TaskStatus.IN_PROGRESS
        task.started_at = time.time()

        try:
            # Simulate task execution based on type
            if task.task_type == TaskType.FILE_CREATION:
                result = self._execute_file_creation(task)
            elif task.task_type == TaskType.FILE_MODIFICATION:
                result = self._execute_file_modification(task)
            elif task.task_type == TaskType.CODE_ANALYSIS:
                result = self._execute_code_analysis(task)
            elif task.task_type == TaskType.TESTING:
                result = self._execute_testing(task)
            elif task.task_type == TaskType.GIT_OPERATION:
                result = self._execute_git_operation(task)
            else:
                result = {"success": True, "message": f"Simulated execution of {task.task_type.value}"}

            task.result = result
            task.status = TaskStatus.COMPLETED if result.get("success", False) else TaskStatus.FAILED

            if not result.get("success", False):
                task.error_message = result.get("error", "Task execution failed")

        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            task.result = {"success": False, "error": str(e)}

        task.completed_at = time.time()
        task.actual_duration = task.completed_at - task.started_at

        return {
            "task_id": task.id,
            "task_name": task.name,
            "status": task.status.value,
            "duration": task.actual_duration,
            "result": task.result
        }

    def _execute_file_creation(self, task: Task) -> Dict[str, Any]:
        """Execute file creation task."""
        try:
            # This would integrate with the actual file creation system
            return {"success": True, "message": f"File creation task simulated: {task.name}"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _execute_file_modification(self, task: Task) -> Dict[str, Any]:
        """Execute file modification task."""
        try:
            # This would integrate with the actual file modification system
            return {"success": True, "message": f"File modification task simulated: {task.name}"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _execute_code_analysis(self, task: Task) -> Dict[str, Any]:
        """Execute code analysis task."""
        try:
            # This would integrate with the actual code analysis system
            return {"success": True, "message": f"Code analysis task simulated: {task.name}"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _execute_testing(self, task: Task) -> Dict[str, Any]:
        """Execute testing task."""
        try:
            # This would integrate with the actual testing system
            return {"success": True, "message": f"Testing task simulated: {task.name}"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _execute_git_operation(self, task: Task) -> Dict[str, Any]:
        """Execute Git operation task."""
        try:
            # This would integrate with the actual Git operations
            return {"success": True, "message": f"Git operation task simulated: {task.name}"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _group_tasks_by_dependency_level(self, tasks: List[Task]) -> Dict[int, List[Task]]:
        """Group tasks by their dependency level for parallel execution."""
        levels = {}
        task_levels = {}

        # Calculate dependency levels
        for task in tasks:
            level = self._calculate_task_level(task, tasks, task_levels)
            task_levels[task.id] = level

            if level not in levels:
                levels[level] = []
            levels[level].append(task)

        return levels

    def _calculate_task_level(self, task: Task, all_tasks: List[Task],
                             calculated_levels: Dict[str, int]) -> int:
        """Calculate the dependency level of a task."""
        if task.id in calculated_levels:
            return calculated_levels[task.id]

        if not task.dependencies:
            calculated_levels[task.id] = 0
            return 0

        max_dep_level = 0
        for dep_id in task.dependencies:
            dep_task = next((t for t in all_tasks if t.id == dep_id), None)
            if dep_task:
                dep_level = self._calculate_task_level(dep_task, all_tasks, calculated_levels)
                max_dep_level = max(max_dep_level, dep_level)

        level = max_dep_level + 1
        calculated_levels[task.id] = level
        return level

    def _generate_recommendations(self, plan: ExecutionPlan) -> List[str]:
        """Generate recommendations for the execution plan."""
        recommendations = []

        # Check for potential issues
        if len(plan.tasks) > 10:
            recommendations.append("Consider breaking this into smaller plans for better manageability")

        if plan.total_estimated_duration > 60:  # More than 1 hour
            recommendations.append("This plan may take significant time. Consider running in background")

        # Check for missing test tasks
        has_tests = any(t.task_type == TaskType.TESTING for t in plan.tasks)
        has_modifications = any(t.task_type in [TaskType.FILE_MODIFICATION, TaskType.FILE_CREATION] for t in plan.tasks)

        if has_modifications and not has_tests:
            recommendations.append("Consider adding testing tasks to verify changes")

        # Check for Git operations
        has_git = any(t.task_type == TaskType.GIT_OPERATION for t in plan.tasks)
        if has_modifications and not has_git:
            recommendations.append("Consider adding Git operations to track changes")

        return recommendations

    def _save_plan(self, plan: ExecutionPlan):
        """Save the execution plan to disk."""
        try:
            plan_file = self.plans_dir / f"{plan.id}.json"
            plan_data = {
                "id": plan.id,
                "name": plan.name,
                "description": plan.description,
                "strategy": plan.strategy.value,
                "status": plan.status.value,
                "created_at": plan.created_at,
                "started_at": plan.started_at,
                "completed_at": plan.completed_at,
                "total_estimated_duration": plan.total_estimated_duration,
                "total_actual_duration": plan.total_actual_duration,
                "success_rate": plan.success_rate,
                "metadata": plan.metadata,
                "tasks": [
                    {
                        "id": task.id,
                        "name": task.name,
                        "description": task.description,
                        "task_type": task.task_type.value,
                        "status": task.status.value,
                        "dependencies": task.dependencies,
                        "estimated_duration": task.estimated_duration,
                        "actual_duration": task.actual_duration,
                        "priority": task.priority,
                        "parameters": task.parameters,
                        "result": task.result,
                        "error_message": task.error_message,
                        "created_at": task.created_at,
                        "started_at": task.started_at,
                        "completed_at": task.completed_at
                    }
                    for task in plan.tasks
                ]
            }

            with open(plan_file, 'w', encoding='utf-8') as f:
                json.dump(plan_data, f, indent=2)

        except Exception as e:
            logger.error(f"Error saving plan: {e}")

    def _auto_commit_changes(self, plan: ExecutionPlan):
        """Automatically commit changes to Git if successful."""
        try:
            # Check if we're in a Git repository
            result = subprocess.run(['git', 'status'], cwd=self.root_path,
                                  capture_output=True, text=True)
            if result.returncode != 0:
                return

            # Add all changes
            subprocess.run(['git', 'add', '.'], cwd=self.root_path, check=True)

            # Commit with plan information
            commit_message = f"Auto-commit: {plan.name}\n\nPlan ID: {plan.id}\nTasks completed: {plan.success_rate:.1%}"
            subprocess.run(['git', 'commit', '-m', commit_message],
                          cwd=self.root_path, check=True)

            logger.info(f"Auto-committed changes for plan {plan.id}")

        except subprocess.CalledProcessError as e:
            logger.warning(f"Auto-commit failed: {e}")
        except Exception as e:
            logger.error(f"Error in auto-commit: {e}")

    def load_plan(self, plan_id: str) -> Optional[ExecutionPlan]:
        """Load a saved execution plan."""
        try:
            plan_file = self.plans_dir / f"{plan_id}.json"
            if not plan_file.exists():
                return None

            with open(plan_file, 'r', encoding='utf-8') as f:
                plan_data = json.load(f)

            # Reconstruct the plan object
            plan = ExecutionPlan(
                id=plan_data["id"],
                name=plan_data["name"],
                description=plan_data["description"],
                strategy=PlanningStrategy(plan_data["strategy"]),
                status=TaskStatus(plan_data["status"]),
                created_at=plan_data["created_at"],
                started_at=plan_data.get("started_at"),
                completed_at=plan_data.get("completed_at"),
                total_estimated_duration=plan_data["total_estimated_duration"],
                total_actual_duration=plan_data["total_actual_duration"],
                success_rate=plan_data["success_rate"],
                metadata=plan_data["metadata"]
            )

            # Reconstruct tasks
            for task_data in plan_data["tasks"]:
                task = Task(
                    id=task_data["id"],
                    name=task_data["name"],
                    description=task_data["description"],
                    task_type=TaskType(task_data["task_type"]),
                    status=TaskStatus(task_data["status"]),
                    dependencies=task_data["dependencies"],
                    estimated_duration=task_data["estimated_duration"],
                    actual_duration=task_data["actual_duration"],
                    priority=task_data["priority"],
                    parameters=task_data["parameters"],
                    result=task_data.get("result"),
                    error_message=task_data.get("error_message"),
                    created_at=task_data["created_at"],
                    started_at=task_data.get("started_at"),
                    completed_at=task_data.get("completed_at")
                )
                plan.tasks.append(task)

            return plan

        except Exception as e:
            logger.error(f"Error loading plan {plan_id}: {e}")
            return None

    def list_plans(self) -> List[Dict[str, Any]]:
        """List all saved execution plans."""
        plans = []

        try:
            for plan_file in self.plans_dir.glob("*.json"):
                with open(plan_file, 'r', encoding='utf-8') as f:
                    plan_data = json.load(f)

                plans.append({
                    "id": plan_data["id"],
                    "name": plan_data["name"],
                    "status": plan_data["status"],
                    "created_at": plan_data["created_at"],
                    "success_rate": plan_data.get("success_rate", 0.0),
                    "task_count": len(plan_data.get("tasks", []))
                })

        except Exception as e:
            logger.error(f"Error listing plans: {e}")

        return sorted(plans, key=lambda x: x["created_at"], reverse=True)
