{"mcpServers": {"ddg-search": {"command": "uvx", "args": ["duckduckgo-mcp-server"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "D:\\Sandeep\\AI_WORKER\\ARG"]}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"]}, "git": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-git", "D:\\Sandeep\\AI_WORKER\\ARG"]}, "sqlite": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sqlite"]}}}